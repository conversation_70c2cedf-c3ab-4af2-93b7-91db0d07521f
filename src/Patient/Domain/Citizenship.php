<?php

declare(strict_types=1);

namespace App\Patient\Domain;

enum Citizenship: string
{
    case AT = 'AT'; // Austria
    case BE = 'BE'; // Belgium
    case BG = 'BG'; // Bulgaria
    case HR = 'HR'; // Croatia
    case CY = 'CY'; // Cyprus
    case CZ = 'CZ'; // Czech Republic
    case DK = 'DK'; // Denmark
    case EE = 'EE'; // Estonia
    case FI = 'FI'; // Finland
    case FR = 'FR'; // France
    case DE = 'DE'; // Germany
    case GR = 'GR'; // Greece
    case HU = 'HU'; // Hungary
    case IE = 'IE'; // Ireland
    case IT = 'IT'; // Italy
    case LV = 'LV'; // Latvia
    case LT = 'LT'; // Lithuania
    case LU = 'LU'; // Luxembourg
    case MT = 'MT'; // Malta
    case NL = 'NL'; // Netherlands
    case PL = 'PL'; // Poland
    case PT = 'PT'; // Portugal
    case RO = 'RO'; // Romania
    case SK = 'SK'; // Slovakia
    case SI = 'SI'; // Slovenia
    case ES = 'ES'; // Spain
    case SE = 'SE'; // Sweden
    case NO = 'NO'; // Norway
    case CH = 'CH'; // Switzerland
    case GB = 'GB'; // United Kingdom
    case IS = 'IS'; // Iceland
    case RU = 'RU'; // Russia
    case UA = 'UA'; // Ukraine
    case BY = 'BY'; // Belarus
    case MD = 'MD'; // Moldova
    case GE = 'GE'; // Georgia
    case AM = 'AM'; // Armenia
    case AZ = 'AZ'; // Azerbaijan
    case OTHER = 'OTHER'; // INNE

    /** @return array<string> */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    public function isEqualsTo(self $other): bool
    {
        return $this->value === $other->value;
    }
}
