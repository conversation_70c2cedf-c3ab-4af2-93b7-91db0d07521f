<?php

declare(strict_types=1);

namespace App\Form\Validator;

use App\Dictionary\DictionaryFacade;
use App\Form\FormService;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Context\ExecutionContextInterface;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

class FormDataConstraintValidator extends ConstraintValidator
{
    public function __construct(
        private readonly DictionaryFacade $dictionaryFacade,
        private readonly FormService $formService,
    ) {
    }

    /** @param array<string, mixed> $value */
    public function validate($value, Constraint $constraint): void
    {
        if (!$constraint instanceof FormDataConstraint) {
            throw new UnexpectedTypeException($constraint, FormDataConstraint::class);
        }

        $fieldsData = $value;
        $flattenFieldsSchema = $constraint->getFlattenFieldsSchema();
        $patientId = $constraint->getPatientId();
        $context = $this->context;

        // Sprawdzanie nieoczekiwanych pól
        $unexpectedFields = $this->validateUnexpectedFields($value, $flattenFieldsSchema, $context);

        if (!empty($unexpectedFields)) {
            return;
        }

        foreach ($flattenFieldsSchema as $fieldSchema) {
            $fieldValue = $fieldsData[$fieldSchema['fieldId']] ?? null;
            $this->validateField(
                $fieldValue,
                $fieldSchema,
                $context,
                $fieldSchema['fieldId'],
                $fieldsData,
                $patientId
            );
        }
    }

    /**
     * @param array<string, mixed> $fieldSchema
     * @param array<string, mixed> $fieldsData
     */
    private function validateField(mixed $value, array $fieldSchema, ExecutionContextInterface $context, string $fieldId,
        array $fieldsData, string $patientId): void
    {
        $constraints = match ($fieldSchema['type']) {
            'text-input-field', 'ckeditor-input-field', 'textarea-input-field' => $this->validateTextInputField($fieldSchema),
            'date-picker-field' => $this->validateDatePickerField($value, $fieldSchema),
            'three-state-level' => $this->validateThreeStateLevelField($fieldSchema),
            'live-select-field' => $this->validateLiveSelectField($value, $fieldSchema),
            'select-input-field' => $this->validateSelectInputField($value, $fieldSchema, $context, $fieldId, $patientId),
            'number-input-field' => $this->validateNumberInputField($fieldSchema),
            'fields-group-table' => $this->validateFieldsGroupTable($value, $fieldSchema, $context, $fieldId, $fieldsData, $patientId),
            'fields-group' => $this->validateFieldsGroup($fieldSchema, $context, $fieldId, $fieldsData, $patientId),
            'fields-set' => $this->validateFieldsSet($fieldSchema, $context, $fieldId, $fieldsData, $patientId),
            'computed-field' => $this->validateComputedField($fieldSchema),
            default => [],
        };

        $this->validateRenderCondition($fieldSchema, $fieldsData, $context, $fieldId);

        foreach ($constraints as $constraint) {
            $violations = $context->getValidator()->validate($value, $constraint);

            foreach ($violations as $violation) {
                $context->buildViolation((string) $violation->getMessage())
                    ->atPath($fieldId)
                    ->addViolation();
            }
        }
    }

    /**
     * @param array<string, mixed> $fieldSchema
     *
     * @return array<int, Constraint>
     */
    private function validateTextInputField(array $fieldSchema): array
    {
        $constraints = [];

        if (isset($fieldSchema['maxTextLength'])) {
            $constraints[] = new Assert\Length(['max' => $fieldSchema['maxTextLength']]);
        }

        return $constraints;
    }

    /**
     * @param array<string, mixed> $fieldSchema
     *
     * @return array<int, Constraint>
     */
    private function validateDatePickerField(mixed $value, array $fieldSchema): array
    {
        if (empty($value)) {
            return [];
        }

        return [
            new Assert\Date(),
            new Assert\GreaterThanOrEqual(!empty($fieldSchema['datePickerMinDate']) ? $fieldSchema['datePickerMinDate'] : '1900-01-01'),
            new Assert\LessThanOrEqual(!empty($fieldSchema['datePickerMaxDate']) ? $fieldSchema['datePickerMaxDate'] : 'now'),
        ];
    }

    /**
     * @param array<string, mixed> $fieldSchema
     *
     * @return array<int, Constraint>
     */
    private function validateThreeStateLevelField(array $fieldSchema): array
    {
        $optionValues = array_map(fn ($option) => $option['value'], $fieldSchema['threeStateLevelOptions']);

        return [
            new Assert\Choice(['choices' => $optionValues]),
        ];
    }

    /**
     * @param array<string, mixed> $fieldSchema
     *
     * @return array<int, Constraint>
     */
    private function validateLiveSelectField(mixed $value, array $fieldSchema): array
    {
        $constraints = [];

        if (isset($fieldSchema['liveSelectDictionary']) && $value !== null) {
            $dictionaryExists = $this->dictionaryFacade->dictionaryValueExists(
                $value,
                $fieldSchema['liveSelectDictionary']
            );
            if (!$dictionaryExists) {
                $constraints[] = new Assert\IsFalse([
                    'message' => 'Wybrana wartość nie istnieje w słowniku '.$fieldSchema['liveSelectDictionary'],
                ]);
            }
        }

        return $constraints;
    }

    /**
     * @param array<string, mixed> $fieldSchema
     *
     * @return array<int, Constraint>
     */
    private function validateSelectInputField(mixed $value, array $fieldSchema, ExecutionContextInterface $context,
        string $fieldId, string $patientId): array
    {
        $constraints = [];

        if (isset($fieldSchema['selectUsePatientProtocolsAsOptions']) && $fieldSchema['selectUsePatientProtocolsAsOptions'] === true) {
            $patientProtocols = $this->formService->getAllPatientProtocols($patientId, true);

            $protocolIds = [];
            foreach ($patientProtocols as $protocol) {
                $protocolIds[] = $protocol->patientProtocolId();
            }

            if (!in_array($value, $protocolIds, true)) {
                $constraints[] = new Assert\IsFalse([
                    'message' => 'Wybrana wartość nie jest jedną z jednostek chorobowych pacjenta lub została dezaktywowana.',
                ]);
            }
        } else {
            $optionValues = array_map(fn ($option) => $option['value'], $fieldSchema['selectOptions']);
            $isMultiple = $fieldSchema['selectMode'] === 'multiple';

            if (!$isMultiple && is_array($value) && count($value) > 1) {
                $context->buildViolation('Wybrano więcej niż jedną wartość dla pola, które powinno mieć tylko jedną wartość.')
                    ->atPath($fieldId)
                    ->addViolation();

                return [];
            }

            if ($value !== null) {
                $constraints[] = new Assert\Choice(['choices' => $optionValues, 'multiple' => $isMultiple, 'min' => 0]);
            }
        }

        return $constraints;
    }

    /**
     * @param array<string, mixed> $fieldSchema
     *
     * @return array<int, Constraint>
     */
    private function validateNumberInputField(array $fieldSchema): array
    {
        $constraints = [
            new Assert\Type(['type' => 'numeric']),
        ];

        if (isset($fieldSchema['numberInputFixedValue'])) {
            $constraints[] = new Assert\Regex([
                'pattern' => '/^\d+(\.\d{1,'.$fieldSchema['numberInputFixedValue'].'})?$/',
                'message' => 'Wartość pola może mieć maksymalnie '.$fieldSchema['numberInputFixedValue'].' miejsca po przecinku.',
            ]);
        }

        return $constraints;
    }

    /**
     * Na ten moment nie jest potrzebne, ponieważ struktura zwracanych danych jest płaska.
     *
     * @param array<string, mixed> $fieldSchema
     * @param array<string, mixed> $fieldsData
     *
     * @return array<int, Constraint>
     */
    private function validateFieldsSet(array $fieldSchema, ExecutionContextInterface $context, string $fieldId,
        array $fieldsData, string $patientId): array
    {
        $constraints = [];

        foreach ($fieldSchema['fieldsSetItems'] as $fieldItem) {
            $fieldValue = $fieldsData[$fieldItem['fieldId']];
            $this->validateField(
                $fieldValue,
                $fieldItem,
                $context,
                $fieldItem['fieldId'],
                $fieldsData,
                $patientId
            );
        }

        return $constraints;
    }

    /**
     *  Na ten moment nie jest potrzebne, ponieważ struktura zwracanych danych jest płaska.
     *
     * @param array<string, mixed> $fieldSchema
     * @param array<string, mixed> $fieldsData
     *
     * @return array<int, Constraint>
     */
    private function validateFieldsGroup(array $fieldSchema, ExecutionContextInterface $context,
        string $fieldId, array $fieldsData, string $patientId): array
    {
        $constraints = [];

        foreach ($fieldSchema['fieldsGroupItems'] as $fieldItem) {
            $fieldValue = $fieldsData[$fieldItem['fieldId']];
            $this->validateField(
                $fieldValue,
                $fieldItem,
                $context,
                $fieldId.'.'.$fieldItem['fieldId'],
                $fieldsData,
                $patientId
            );
        }

        return $constraints;
    }

    /**
     * @param array<string, mixed> $fieldSchema
     * @param array<string, mixed> $fieldsData
     *
     * @return array<int, Constraint>
     */
    private function validateFieldsGroupTable(mixed $value, array $fieldSchema, ExecutionContextInterface $context,
        string $fieldId, array $fieldsData, string $patientId): array
    {
        $constraints = [];

        if (!is_array($value)) {
            $context->buildViolation('Nieprawidłowa wartość dla tabeli. Oczekiwano tablicy wartości.')
                ->atPath($fieldId)
                ->addViolation();

            return $constraints;
        }

        $uniqFields = [];
        foreach ($value as $rowIndex => $row) {
            foreach ($fieldSchema['fieldsGroupRowItems'] as $fieldItem) {
                if (!array_key_exists($fieldItem['fieldId'], $row)) {
                    continue;
                }

                $fieldValue = $row[$fieldItem['fieldId']];
                $this->validateField(
                    $fieldValue,
                    $fieldItem,
                    $context,
                    $fieldId.'.'.$rowIndex.'.'.$fieldItem['fieldId'],
                    $fieldsData,
                    $patientId
                );

                // Check if the field has selectUsePatientProtocolsAsOptions set to true
                if (isset($fieldItem['selectUsePatientProtocolsAsOptions']) && $fieldItem['selectUsePatientProtocolsAsOptions'] === true) {
                    $uniqFields[$fieldItem['fieldId']][] = $fieldValue;
                }
            }
        }

        // Check if all protocol values are unique for each field
        foreach ($uniqFields as $fieldRowId => $values) {
            if (count($values) !== count(array_unique($values))) {
                $context->buildViolation('Wartości pola z wyborem jednostki chorobowej muszą być unikalne w ramach tabeli.')
                    ->atPath((string) $fieldRowId)
                    ->addViolation();
            }
        }

        return $constraints;
    }

    /**
     * @param array<string, mixed> $fieldSchema
     *
     * @return array<int, Constraint>
     */
    private function validateComputedField(array $fieldSchema): array
    {
        $constraints = [
            new Assert\Type(['type' => 'numeric']),
        ];

        if (isset($fieldSchema['numberInputFixedValue'])) {
            $constraints[] = new Assert\Regex([
                'pattern' => '/^\d+(\.\d{1,'.$fieldSchema['numberInputFixedValue'].'})?$/',
                'message' => 'Wartość pola może mieć maksymalnie '.$fieldSchema['numberInputFixedValue'].' miejsca po przecinku.',
            ]);
        }

        return $constraints;
    }

    /**
     * @param array<string, mixed> $fieldSchema
     * @param array<string, mixed> $fieldsData
     */
    private function validateRenderCondition(array $fieldSchema, array $fieldsData, ExecutionContextInterface $context, string $fieldId): void
    {
        if (empty($fieldSchema['renderConditions'])) {
            return;
        }

        if ($fieldSchema['type'] === 'fields-set') {
            return;
        }

        if ($fieldSchema['type'] === 'fields-group') {
            return;
        }

        foreach ($fieldSchema['renderConditions'] as $renderCondition) {
            $dependentFieldValue = $fieldsData[$renderCondition['dependentFieldId']] ?? null;
            if ($dependentFieldValue !== $renderCondition['conditionalValue']) {
                $fieldValue = $fieldsData[$fieldSchema['fieldId']] ?? null;
                if ($fieldValue !== $fieldSchema['value']) {
                    $context->buildViolation('Pole "{{ field }}" powinno zawierać domyślną wartość "{{ value }}", ponieważ warunek dla tego pola nie został spełniony.')
                        ->setParameter('{{ field }}', $fieldSchema['label'])
                        ->setParameter('{{ value }}', $fieldSchema['value'])
                        ->atPath($fieldId)
                        ->addViolation();
                }

                return;
            }
        }

        if (isset($fieldsData[$fieldId]) && is_array($fieldsData[$fieldId])) {
            foreach ($fieldsData[$fieldId] as $childFieldId => $childFieldValue) {
                if (isset($fieldSchema[$childFieldId])) {
                    $this->validateRenderCondition($fieldSchema[$childFieldId], $fieldsData, $context, $childFieldId);
                }
            }
        }
    }

    /**
     * @param array<string, mixed>             $value
     * @param array<int, array<string, mixed>> $schema
     *
     * @return array<string>
     */
    private function validateUnexpectedFields(array $value, array $schema, ExecutionContextInterface $context): array
    {
        $flattenData = $this->flattenFieldId($value);
        $expectedFields = array_column($schema, 'fieldId');
        $unexpectedFields = array_diff(array_keys($flattenData), $expectedFields);

        foreach ($unexpectedFields as $unexpectedField) {
            $context->buildViolation('Nieoczekiwane pole "{{ field }}" znajduje się w przesłanych danych.')
                ->setParameter('{{ field }}', $unexpectedField)
                ->atPath($unexpectedField)
                ->addViolation();
        }

        return $unexpectedFields;
    }

    /**
     * @param array<string, mixed> $array
     *
     * @return array<string, string>
     */
    private function flattenFieldId(array $array): array
    {
        $result = [];
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                foreach ($this->flattenFieldId($value) as $subKey => $subValue) {
                    $result[$subKey] = $subKey;
                }
            } else {
                $result[$key] = $key;
            }
        }

        return $result;
    }
}
