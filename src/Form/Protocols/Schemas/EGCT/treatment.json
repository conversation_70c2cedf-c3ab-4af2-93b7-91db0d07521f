{"formTitle": "", "sections": [{"title": "LECZENIE", "rootFieldsIds": ["treatmentStartDate", "treatmentProtocol", "chemotherapy", "radiotherapy", "surgery", "hsct", "hormoneTherapy", "immunotherapy", "targetedTherapy", "otherTherapy", "treatmentEndDate", "reasonForEndingTreatment"]}, {"title": "TOKSYCZNOŚCI", "rootFieldsIds": ["toxicity"]}, {"title": "ODPOWIEDŹ NA LECZENIE", "rootFieldsIds": ["crMarkerNormalization", "patientStatus"]}, {"title": "KOMENTARZE", "rootFieldsIds": ["comment"]}], "fields": [{"fieldId": "treatmentStartDate", "label": "Data rozpoczęcia leczenia", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": null, "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": []}, {"fieldId": "treatmentProtocol", "label": "<PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "treatmentGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "EGCT-PROTOCOL", "liveSelectItemsPerPage": 100, "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "fieldWidth": 325, "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/14Dj_AWsJGAHFDF6ef9KpzYE8aGoX1zfMfbYq1qykRUU/edit?tab=t.0#heading=h.ghz84lmcl3qy", "tooltipWidth": 170}, {"fieldId": "chemotherapy", "label": "Chemioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "chemotherapyStartDate", "label": "Data rozpoczęcia chemioterapii", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "chemotherapy", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}]}, {"fieldId": "chemotherapyType", "label": "Schemat chemioterapii", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "chemotherapy", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "VBP", "value": "VBP"}, {"label": "VIP", "value": "VIP"}, {"label": "BEP", "value": "BEP"}, {"label": "Inny", "value": "Inny"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}], "fieldWidth": 200, "children": [{"fieldId": "chemotherapyTypeOther", "label": "<PERSON>y schemat", "errors": [], "type": "text-input-field", "disabled": false, "parentId": "chemotherapyType", "value": "", "maxTextLength": 255, "placeholder": "", "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}, {"conditionalValue": "Inny", "conditionType": "equal", "dependentFieldId": "chemotherapyType"}], "fieldWidth": 500}]}, {"fieldId": "chemotherapyNumberOfCycles", "label": "Liczba podanych cykli", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "chemotherapy", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "1", "value": "1"}, {"label": "2", "value": "2"}, {"label": "3", "value": "3"}, {"label": "4", "value": "4"}, {"label": "5", "value": "5"}, {"label": "6", "value": "6"}, {"label": "7", "value": "7"}, {"label": "8", "value": "8"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}], "fieldWidth": 200}, {"fieldId": "chemotherapyModification", "label": "Modyfikacja chemioterapii", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "chemotherapy", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}], "children": [{"fieldId": "chemotherapyModificationDoseChange", "label": "<PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "chemotherapyModification", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapyModification"}]}, {"fieldId": "chemotherapyModificationChangeType", "label": "<PERSON>y schemat", "errors": [], "type": "text-input-field", "disabled": false, "parentId": "chemotherapyModification", "value": "", "maxTextLength": 255, "placeholder": "", "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapyModification"}], "fieldWidth": 500}]}]}, {"fieldId": "radiotherapy", "label": "Radioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "surgery", "label": "Chirurgia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "surgeryBeforeChemotherapy", "label": "Zabieg chirurgiczny w trakcie chemioterapii", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "surgery", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "surgery"}], "children": [{"fieldId": "surgeryBeforeChemotherapyGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "surgeryBeforeChemotherapy", "fieldsGroupItems": [{"fieldId": "surgeryBeforeChemotherapyDate", "label": "Data zabiegu", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "surgeryBeforeChemotherapyGroup1", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "surgeryBeforeChemotherapyType", "label": "<PERSON><PERSON><PERSON>abi<PERSON>", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "surgeryBeforeChemotherapyGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"label": "Nieradykalne", "value": "Nieradykalne"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "fieldWidth": 300}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "surgery"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "surgeryBeforeChemotherapy"}], "fieldsGroupColumns": 2, "fieldWidth": 500}]}, {"fieldId": "surgeryAfterChemotherapy", "label": "Chirurgia wtórna (po chemioterapii)", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "surgery", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "surgery"}], "children": [{"fieldId": "surgeryAfterChemotherapyGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "surgeryAfterChemotherapy", "fieldsGroupItems": [{"fieldId": "surgeryAfterChemotherapyDate", "label": "Data zabiegu", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "surgeryAfterChemotherapyGroup1", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "surgeryAfterChemotherapyType", "label": "<PERSON><PERSON><PERSON>abi<PERSON>", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "surgeryAfterChemotherapyGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"label": "Nieradykalne", "value": "Nieradykalne"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "fieldWidth": 300}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "surgery"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "surgeryAfterChemotherapy"}], "fieldsGroupColumns": 2, "fieldWidth": 500}]}]}, {"fieldId": "hsct", "label": "Przeszczep", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "hormoneTherapy", "label": "Hormonoterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "immunotherapy", "label": "Immunoterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "targetedTherapy", "label": "<PERSON><PERSON><PERSON> celowana", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "otherTherapy", "label": "<PERSON><PERSON> metody <PERSON>", "errors": [], "type": "text-input-field", "disabled": false, "parentId": null, "value": "", "maxTextLength": 255, "placeholder": "", "renderConditions": [], "fieldWidth": 500}, {"fieldId": "treatmentEndDate", "label": "Data zakończenia leczenia", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": null, "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": false, "renderConditions": []}, {"fieldId": "reasonForEndingTreatment", "label": "Przyczyna zakończenia leczenia", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Wyleczenie", "value": "PZL1"}, {"label": "Toksyczności i działania niepożądane", "value": "PZL2"}, {"label": "Wyczerpanie możliwości terapeutycznych", "value": "PZL3"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "PZL4"}, {"label": "Decyzja rodzica", "value": "PZL5"}, {"label": "Zgon", "value": "PZL6"}, {"label": "<PERSON><PERSON>", "value": "PZL7"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "fieldWidth": 400}, {"fieldId": "toxicity", "label": "Toksyczności leczenia", "errors": [], "type": "fields-set", "disabled": false, "parentId": null, "fieldsSetItems": [{"fieldId": "toxicityWBC", "label": "WBC", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityGranulocyte", "label": "Granulocyty", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityHaemoglobin", "label": "Hemoglobina", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityThrombocytes", "label": "Płytki krwi", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityHepato", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityNefro", "label": "Nefrotok<PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityCardio", "label": "Kardioto<PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityEnteritis", "label": "Zapalenie j<PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityNeuro", "label": "Neurotoks<PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityPulmo", "label": "Pul<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityInfection", "label": "Infekcje", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityAnaphylacticReaction", "label": "Reakcja anafilaktyczna", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}], "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/14Dj_AWsJGAHFDF6ef9KpzYE8aGoX1zfMfbYq1qykRUU/edit?tab=t.0#heading=h.hns4l24c3mb0", "tooltipWidth": 170}, {"fieldId": "crMarkerNormalization", "label": "Normalizacja markerów", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Po wstępnym zabiegu chirurgicznym", "value": "Po wstępnym zabiegu chirurgicznym"}, {"label": "Po chemioterapii", "value": "Po chemioterapii"}, {"label": "Brak normalizacji markerów", "value": "Brak normalizacji markerów"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "fieldWidth": 300}, {"fieldId": "patientStatus", "label": "Status pacjenta", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "SD", "value": "SD"}, {"label": "CR", "value": "CR"}, {"label": "Progresja", "value": "progression"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "children": [{"fieldId": "sdDate", "label": "Data potwierdzenia stabilnej choroby", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "patientStatus", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "SD", "conditionType": "equal", "dependentFieldId": "patientStatus"}]}, {"fieldId": "crDate", "label": "Data remisji całkowitej", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "patientStatus", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "CR", "conditionType": "equal", "dependentFieldId": "patientStatus"}]}, {"fieldId": "progressionGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "patientStatus", "fieldsGroupItems": [{"fieldId": "progressionDate", "label": "Data progresji", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "patientStatus", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "progressionType", "label": "<PERSON><PERSON> prog<PERSON>", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "patientStatus", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Lokalna", "value": "Lokalna"}, {"label": "Odległa", "value": "Odległa"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "progression", "conditionType": "equal", "dependentFieldId": "patientStatus"}], "fieldWidth": 580, "fieldsGroupColumns": 2}, {"fieldId": "progressionSet", "label": "", "errors": [], "type": "fields-set", "disabled": false, "parentId": "patientStatus", "fieldsSetItems": [{"fieldId": "progressionAFP", "label": "AFP", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "progressionSet", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Podwyższony", "value": "Podwyższony"}, {"label": "W normie", "value": "W normie"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": []}, {"fieldId": "progressionBHCG", "label": "βHCG", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "progressionSet", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Podwyższony", "value": "Podwyższony"}, {"label": "W normie", "value": "W normie"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": []}, {"fieldId": "progressionLDH", "label": "LDH", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "progressionSet", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Podwyższony", "value": "Podwyższony"}, {"label": "W normie", "value": "W normie"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": []}, {"fieldId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON> markery", "errors": [], "type": "text-input-field", "disabled": false, "parentId": "progressionSet", "value": "", "maxTextLength": 255, "placeholder": "", "renderConditions": [], "fieldWidth": 500}], "renderConditions": [{"conditionalValue": "progression", "conditionType": "equal", "dependentFieldId": "patientStatus"}]}, {"fieldId": "progressionChemotherapy", "label": "Chemioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "patientStatus", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "progression", "conditionType": "equal", "dependentFieldId": "patientStatus"}], "children": [{"fieldId": "progressionChemotherapyType", "label": "Schemat chemioterapii", "errors": [], "type": "text-input-field", "disabled": false, "parentId": "progressionChemotherapy", "value": "", "maxTextLength": 255, "placeholder": "", "renderConditions": [{"conditionalValue": "progression", "conditionType": "equal", "dependentFieldId": "patientStatus"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "progressionChemotherapy"}], "fieldWidth": 500}, {"fieldId": "progressionChemotherapyNumberOfCycles", "label": "Liczba cykli", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "progressionChemotherapy", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "1", "value": "1"}, {"label": "2", "value": "2"}, {"label": "3", "value": "3"}, {"label": "4", "value": "4"}, {"label": "5", "value": "5"}, {"label": "6", "value": "6"}, {"label": "7", "value": "7"}, {"label": "8", "value": "8"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "progression", "conditionType": "equal", "dependentFieldId": "patientStatus"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "progressionChemotherapy"}]}]}, {"fieldId": "progressionRadiotherapy", "label": "Radioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "patientStatus", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "progression", "conditionType": "equal", "dependentFieldId": "patientStatus"}]}, {"fieldId": "progressionSurgery", "label": "Chirurgia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "patientStatus", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "progression", "conditionType": "equal", "dependentFieldId": "patientStatus"}]}, {"fieldId": "progressionGroup2", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "patientStatus", "fieldsGroupItems": [{"fieldId": "progressionEndTreatmentDate", "label": "Data zakończenia leczenia progresji", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "patientStatus", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "progressionEndTreatmentStatus", "label": "Status po leczeniu progresji", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "patientStatus", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "CR", "value": "CR"}, {"label": "Progresja", "value": "Progresja"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "progression", "conditionType": "equal", "dependentFieldId": "patientStatus"}], "fieldWidth": 580, "fieldsGroupColumns": 2}]}, {"fieldId": "comment", "label": "", "errors": [], "type": "textarea-input-field", "disabled": false, "parentId": null, "value": "", "textAreaInputRows": 4, "maxTextLength": 1000, "placeholder": "", "renderConditions": []}]}