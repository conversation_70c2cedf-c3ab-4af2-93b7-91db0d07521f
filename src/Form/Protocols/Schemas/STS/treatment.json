{"formTitle": "", "sections": [{"title": "LECZENIE", "rootFieldsIds": ["treatmentStartDate", "treatmentProtocol", "chemotherapy", "radiotherapy", "surgery", "hsct", "hormoneTherapy", "immunotherapy", "targetedTherapy", "otherTherapy", "treatmentEndDate", "reasonForEndingTreatment", "maintenanceTreatment"]}, {"title": "TOKSYCZNOŚCI", "rootFieldsIds": ["toxicity"]}, {"title": "ODPOWIEDŹ NA LECZENIE", "rootFieldsIds": ["patientStatus"]}, {"title": "KOMENTARZE", "rootFieldsIds": ["comment"]}], "fields": [{"fieldId": "treatmentStartDate", "label": "Data rozpoczęcia leczenia", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": null, "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": []}, {"fieldId": "treatmentProtocol", "label": "<PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "histPatDiagnosis", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "STS-PROTOCOL", "liveSelectItemsPerPage": 100, "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1GB1m5mS2nS8kg6Ve6cNKCuXibFYwR61T_O8q2iuQExo/edit?tab=t.0#heading=h.ghz84lmcl3qy", "tooltipWidth": 170}, {"fieldId": "chemotherapy", "label": "Chemioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "chemotherapyTherapeuticArm", "label": "<PERSON><PERSON><PERSON>", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "chemotherapy", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "STS-THERAPY-ARM", "liveSelectItemsPerPage": 100, "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}], "fieldWidth": 240}, {"fieldId": "chemotherapyCycleGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "chemotherapy", "fieldsGroupItems": [{"fieldId": "chemotherapyCycle1", "label": "Cykl 1", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "chemotherapyCycleGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "STS-CYCLE", "liveSelectItemsPerPage": 100, "placeholder": "", "selectUsePatientProtocolsAsOptions": false}, {"fieldId": "chemotherapyCycle2", "label": "Cykl 2", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "chemotherapyCycleGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "STS-CYCLE", "liveSelectItemsPerPage": 100, "placeholder": "", "selectUsePatientProtocolsAsOptions": false}, {"fieldId": "chemotherapyCycle3", "label": "Cykl 3", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "chemotherapyCycleGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "STS-CYCLE", "liveSelectItemsPerPage": 100, "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}], "fieldWidth": 500}, {"fieldId": "chemotherapyResponseAfter3Months", "label": "Odpowiedź na leczenie po 3 cyklu", "errors": [], "type": "fields-set", "disabled": false, "parentId": "chemotherapy", "fieldsSetItems": [{"fieldId": "chemotherapyResponseAfter3MonthsTumorReduction", "label": "Redukcja guza [%]", "errors": [], "type": "text-input-field", "disabled": false, "parentId": "chemotherapyResponseAfter3Months", "value": "", "maxTextLength": 255, "placeholder": "", "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}], "fieldLabelWidth": 260, "fieldWidth": 260}, {"fieldId": "chemotherapyResponseAfter3MonthsTypeOfImaging", "label": "<PERSON><PERSON><PERSON>", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "chemotherapyResponseAfter3Months", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "USG", "value": "USG"}, {"label": "MR", "value": "MR"}, {"label": "CT", "value": "CT"}, {"label": "nie wykonano badania", "value": "NONE"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}], "fieldLabelWidth": 260, "fieldWidth": 260}, {"fieldId": "chemotherapyResponseAfter3MonthsDateOfImaging", "label": "Data badania", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "chemotherapyResponseAfter3Months", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}], "fieldLabelWidth": 260, "fieldWidth": 260}, {"fieldId": "chemotherapyResponseAfter3MonthsPatientStatus", "label": "Status pacjenta po 3 cyklach", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "chemotherapyResponseAfter3Months", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "CR", "value": "CR"}, {"label": "VGPR", "value": "VGPR"}, {"label": "PR>2/3", "value": "PR>2/3"}, {"label": "PR<2/3", "value": "PR<2/3"}, {"label": "SD", "value": "SD"}, {"label": "PD", "value": "PD"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}], "fieldLabelWidth": 260, "fieldWidth": 260, "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1GB1m5mS2nS8kg6Ve6cNKCuXibFYwR61T_O8q2iuQExo/edit?tab=t.0#heading=h.azl95ziqi9ab", "tooltipWidth": 170}, {"fieldId": "chemotherapyResponseAfter3MonthsTherpaeuticArmChange", "label": "Zamiana ramienia terapeutycznego", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "chemotherapyResponseAfter3Months", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}], "fieldLabelWidth": 260, "children": [{"fieldId": "responseAfter3MonthsTherpaeuticArmChangeNewArm", "label": "<PERSON><PERSON><PERSON>", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "chemotherapyResponseAfter3MonthsTherpaeuticArmChange", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "STS-THERAPY-ARM", "liveSelectItemsPerPage": 100, "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapyResponseAfter3MonthsTherpaeuticArmChange"}], "fieldWidth": 260}]}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}]}, {"fieldId": "chemotherapyCycleGroup2", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "chemotherapy", "fieldsGroupItems": [{"fieldId": "chemotherapyCycle4", "label": "Cykl 4", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "chemotherapyCycleGroup2", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "STS-CYCLE", "liveSelectItemsPerPage": 100, "placeholder": "", "selectUsePatientProtocolsAsOptions": false}, {"fieldId": "chemotherapyCycle5", "label": "Cykl 5", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "chemotherapyCycleGroup2", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "STS-CYCLE", "liveSelectItemsPerPage": 100, "placeholder": "", "selectUsePatientProtocolsAsOptions": false}, {"fieldId": "chemotherapyCycle6", "label": "Cykl 6", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "chemotherapyCycleGroup2", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "STS-CYCLE", "liveSelectItemsPerPage": 100, "placeholder": "", "selectUsePatientProtocolsAsOptions": false}, {"fieldId": "chemotherapyCycle7", "label": "Cykl 7", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "chemotherapyCycleGroup2", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "STS-CYCLE", "liveSelectItemsPerPage": 100, "placeholder": "", "selectUsePatientProtocolsAsOptions": false}, {"fieldId": "chemotherapyCycle8", "label": "Cykl 8", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "chemotherapyCycleGroup2", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "STS-CYCLE", "liveSelectItemsPerPage": 100, "placeholder": "", "selectUsePatientProtocolsAsOptions": false}, {"fieldId": "chemotherapyCycle9", "label": "Cykl 9", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "chemotherapyCycleGroup2", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "STS-CYCLE", "liveSelectItemsPerPage": 100, "placeholder": "", "selectUsePatientProtocolsAsOptions": false}, {"fieldId": "chemotherapyCycle10", "label": "Cykl 10", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "chemotherapyCycleGroup2", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "STS-CYCLE", "liveSelectItemsPerPage": 100, "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}], "fieldsGroupColumns": 7}]}, {"fieldId": "radiotherapy", "label": "Radioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "radiotherapyGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "radiotherapy", "fieldsGroupItems": [{"fieldId": "radiotherapyStartDate", "label": "Data rozpoczęcia Rx", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "radiotherapyGroup1", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "radiotherapyEndDate", "label": "Data zakończenia Rx", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "radiotherapyGroup1", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "radiotherapy"}], "fieldWidth": 800}, {"fieldId": "radiotherapyGroup2", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "radiotherapy", "fieldsGroupItems": [{"fieldId": "radiotherapyType", "label": "<PERSON><PERSON>", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "radiotherapyGroup2", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "konwencjonalny", "value": "konwencjonalny"}, {"label": "protonoterapia", "value": "protonoterapia"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}, {"fieldId": "radiotherapyDose", "label": "Dawka", "errors": [], "type": "text-input-field", "disabled": false, "parentId": "radiotherapyGroup2", "value": "", "maxTextLength": 255, "placeholder": "", "fieldWidth": 260}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "radiotherapy"}], "fieldWidth": 800}, {"fieldId": "radiotherapyLocation", "label": "Miejsce naświetlania", "errors": [], "type": "fields-set", "disabled": false, "parentId": "radiotherapy", "fieldsSetItems": [{"fieldId": "radiotherapyPrimaryTumor", "label": "Guz pierwotny", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "radiotherapyLocation", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "radiotherapy"}]}, {"fieldId": "radiotherapyResidualLesions", "label": "Pozostał<PERSON><PERSON><PERSON> guza", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "radiotherapyLocation", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "radiotherapy"}]}, {"fieldId": "radiotherapyMetastases", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "radiotherapyLocation", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "radiotherapy"}]}, {"fieldId": "radiotherapyLymphNodes", "label": "Węzły chłonne", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "radiotherapyLocation", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "radiotherapy"}]}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "radiotherapy"}]}]}, {"fieldId": "surgery", "label": "Chirurgia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "surgeryDate", "label": "Data zabiegu operacyjnego", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "surgery", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "surgery"}]}, {"fieldId": "surgeryLocation", "label": "Lokalizacja", "errors": [], "type": "fields-set", "disabled": false, "parentId": "surgery", "fieldsSetItems": [{"fieldId": "surgeryPrimary<PERSON>umor", "label": "Guz pierwotny", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "surgeryLocation", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "surgery"}]}, {"fieldId": "surgeryLymphNodes", "label": "Węzły chłonne", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "surgeryLocation", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "surgery"}]}, {"fieldId": "surgeryMetastases", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "surgeryLocation", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "surgery"}]}, {"fieldId": "surgeryType", "label": "<PERSON><PERSON><PERSON>abi<PERSON>", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "surgeryLocation", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Biopsja", "value": "Biopsja"}, {"label": "R0 - Guz usunięty w całości makroskopowo i mikroskopowo", "value": "R0"}, {"label": "R1 - Guz usunięty ma<PERSON>roskopowo, obecne pozostałości mikroskopowe", "value": "R1"}, {"label": "R2 - Guz usunięty niekompletnie, obecne pozostałości makroskopowe", "value": "R2"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "surgery"}], "fieldWidth": 600}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "surgery"}]}]}, {"fieldId": "hsct", "label": "Przeszczep", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "hormoneTherapy", "label": "Hormonoterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "immunotherapy", "label": "Immunoterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "targetedTherapy", "label": "<PERSON><PERSON><PERSON> celowana", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "otherTherapy", "label": "<PERSON><PERSON> metody <PERSON>", "errors": [], "type": "text-input-field", "disabled": false, "parentId": null, "value": "", "maxTextLength": 255, "placeholder": "", "renderConditions": [], "fieldWidth": 500}, {"fieldId": "treatmentEndDate", "label": "Data zakończenia leczenia", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": null, "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": false, "renderConditions": []}, {"fieldId": "reasonForEndingTreatment", "label": "Przyczyna zakończenia leczenia", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Wyleczenie", "value": "PZL1"}, {"label": "Toksyczności i działania niepożądane", "value": "PZL2"}, {"label": "Wyczerpanie możliwości terapeutycznych", "value": "PZL3"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "PZL4"}, {"label": "Decyzja rodzica", "value": "PZL5"}, {"label": "Zgon", "value": "PZL6"}, {"label": "<PERSON><PERSON>", "value": "PZL7"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "fieldWidth": 400}, {"fieldId": "maintenanceTreatment", "label": "Leczenie podtrzymujące", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "maintenanceTreatmentGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "maintenanceTreatment", "fieldsGroupItems": [{"fieldId": "maintenanceTreatmentStartDate", "label": "Data rozpoczęcia leczenia podtrzymującego", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "maintenanceTreatmentGroup1", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "maintenanceTreatmentEndDate", "label": "Data zakończenia leczenia podtrzymującego", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "maintenanceTreatmentGroup1", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "maintenanceTreatmentType", "label": "Rodzaj leczenia podtrzymującego", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "maintenanceTreatmentGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "O-TI/E", "value": "O-TI/E"}, {"label": "Cyclophosphamide  + Vinblstine", "value": "Cyclophosphamide  + Vinblstine"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "maintenanceTreatment"}]}]}, {"fieldId": "toxicity", "label": "Toksyczności leczenia", "errors": [], "type": "fields-set", "disabled": false, "parentId": null, "fieldsSetItems": [{"fieldId": "toxicityWBC", "label": "WBC", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityGranulocyte", "label": "Granulocyty", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityHaemoglobin", "label": "Hemoglobina", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityThrombocytes", "label": "Płytki krwi", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityHepato", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityNefro", "label": "Nefrotok<PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityCardio", "label": "Kardioto<PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityEnteritis", "label": "Zapalenie j<PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityNeuro", "label": "Neurotoks<PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityPulmo", "label": "Pul<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityInfection", "label": "Infekcje", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityAnaphylacticReaction", "label": "Reakcja anafilaktyczna", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}], "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1GB1m5mS2nS8kg6Ve6cNKCuXibFYwR61T_O8q2iuQExo/edit?tab=t.0#heading=h.hns4l24c3mb0", "tooltipWidth": 170}, {"fieldId": "patientStatus", "label": "Status pacjenta", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "SD", "value": "SD"}, {"label": "CR", "value": "CR"}, {"label": "Progresja", "value": "progression"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "children": [{"fieldId": "sdDate", "label": "Data potwierdzenia stabilnej choroby", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "patientStatus", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "SD", "conditionType": "equal", "dependentFieldId": "patientStatus"}]}, {"fieldId": "crDate", "label": "Data remisji całkowitej", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "patientStatus", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "CR", "conditionType": "equal", "dependentFieldId": "patientStatus"}]}, {"fieldId": "progressionDate", "label": "Data progresji", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "patientStatus", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "progression", "conditionType": "equal", "dependentFieldId": "patientStatus"}]}, {"fieldId": "progressionType", "label": "<PERSON><PERSON> prog<PERSON>", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "patientStatus", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Lokalna", "value": "Lokalna"}, {"label": "Odległa", "value": "Odległa"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "progression", "conditionType": "equal", "dependentFieldId": "patientStatus"}]}, {"fieldId": "progressionTherapeuticArm", "label": "<PERSON><PERSON><PERSON>", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "patientStatus", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "STS-THERAPY-ARM", "liveSelectItemsPerPage": 100, "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "progression", "conditionType": "equal", "dependentFieldId": "patientStatus"}], "fieldWidth": 240}, {"fieldId": "progressionRadiotherapy", "label": "Radioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "patientStatus", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "progression", "conditionType": "equal", "dependentFieldId": "patientStatus"}]}, {"fieldId": "progressionSurgery", "label": "Zabieg operacyjny", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "patientStatus", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "progression", "conditionType": "equal", "dependentFieldId": "patientStatus"}], "children": [{"fieldId": "progressionSurgeryDate", "label": "Data zabiegu operacyjnego", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "progressionSurgery", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "progression", "conditionType": "equal", "dependentFieldId": "patientStatus"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "progressionSurgery"}]}, {"fieldId": "progressionSurgeryLocation", "label": "Lokalizacja", "errors": [], "type": "fields-set", "disabled": false, "parentId": "progressionSurgery", "fieldsSetItems": [{"fieldId": "progressionSurgeryPrimaryTumor", "label": "Guz pierwotny", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "progressionSurgeryLocation", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "progression", "conditionType": "equal", "dependentFieldId": "patientStatus"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "progressionSurgery"}]}, {"fieldId": "progressionSurgeryLymphNodes", "label": "Węzły chłonne", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "progressionSurgeryLocation", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "progression", "conditionType": "equal", "dependentFieldId": "patientStatus"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "progressionSurgery"}]}, {"fieldId": "progressionSurgeryMetastases", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "progressionSurgeryLocation", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "progression", "conditionType": "equal", "dependentFieldId": "patientStatus"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "progressionSurgery"}]}, {"fieldId": "progressionSurgeryType", "label": "<PERSON><PERSON><PERSON>abi<PERSON>", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "progressionSurgeryLocation", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Biopsja", "value": "Biopsja"}, {"label": "R0 - Guz usunięty w całości makroskopowo i mikroskopowo", "value": "R0"}, {"label": "R1 - Guz usunięty ma<PERSON>roskopowo, obecne pozostałości mikroskopowe", "value": "R1"}, {"label": "R2 - Guz usunięty niekompletnie, obecne pozostałości makroskopowe", "value": "R2"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "progression", "conditionType": "equal", "dependentFieldId": "patientStatus"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "progressionSurgery"}], "fieldWidth": 700}], "renderConditions": [{"conditionalValue": "progression", "conditionType": "equal", "dependentFieldId": "patientStatus"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "progressionSurgery"}]}]}, {"fieldId": "progressionEndTreatmentDate", "label": "Data zakończenia leczenia progresjii", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "patientStatus", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "progression", "conditionType": "equal", "dependentFieldId": "patientStatus"}]}, {"fieldId": "progressionEndTreatmentStatus", "label": "Status po leczeniu progresji", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "patientStatus", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Całkowita remisja", "value": "Całkowita remisja"}, {"label": "Częściowa remisja", "value": "Częściowa remisja"}, {"label": "Stabilna choroba", "value": "Stabilna choroba"}, {"label": "Progresja", "value": "Progresja"}, {"label": "Wznowa", "value": "Wznowa"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "progression", "conditionType": "equal", "dependentFieldId": "patientStatus"}]}]}, {"fieldId": "comment", "label": "", "errors": [], "type": "textarea-input-field", "disabled": false, "parentId": null, "value": "", "textAreaInputRows": 4, "maxTextLength": 1000, "placeholder": "", "renderConditions": []}]}