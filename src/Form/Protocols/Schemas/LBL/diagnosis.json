{"formTitle": "", "sections": [{"title": "CHOROBY WSPÓŁISTNIEJĄCE", "rootFieldsIds": ["predispositionToCancer", "immunodeficiency", "otherComorbiditiesOrTherapies", "assessmentOfThePatientsCondition"]}, {"title": "OPIS NOWOTWORU", "rootFieldsIds": ["dateOfDiagnosis", "descriptionAndLocationOfTheTumor", "primaryTumour", "cnsInvolvement", "boneMarrowInvolvement", "metastases", "cancerStage"]}, {"title": "DIAGNOZA HISTOPATOLOGICZNA", "rootFieldsIds": ["dateOfHistPatDiagnosis", "pathologicalDiagnosis", "clinicalStageOfCancer", "riskGroup", "histPatDiagnosis"]}, {"title": "DODATKOWE BADANIA", "rootFieldsIds": ["immunohistochemistry"]}, {"title": "KLASYFIKACJA", "rootFieldsIds": ["icd10", "iccc3"]}, {"title": "KOMENTARZ", "rootFieldsIds": ["comment"]}], "fields": [{"fieldId": "predispositionToCancer", "label": "Predyspozycje do nowotworzenia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "immunodeficiency", "label": "Niedobór <PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "otherComorbiditiesOrTherapies", "label": "Inne choroby współistniejące lub terapie", "errors": [], "type": "textarea-input-field", "disabled": false, "parentId": null, "value": "", "textAreaInputRows": 4, "maxTextLength": 1000, "placeholder": "", "renderConditions": []}, {"fieldId": "assessmentOfThePatientsCondition", "label": "<PERSON><PERSON>na stanu pacjenta wg s<PERSON><PERSON>/<PERSON>nsky", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": true, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "10", "value": "10"}, {"label": "20", "value": "20"}, {"label": "30", "value": "30"}, {"label": "40", "value": "40"}, {"label": "50", "value": "50"}, {"label": "60", "value": "60"}, {"label": "70", "value": "70"}, {"label": "80", "value": "80"}, {"label": "90", "value": "90"}, {"label": "100", "value": "100"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": []}, {"fieldId": "dateOfDiagnosis", "label": "Data rozpoznania", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": null, "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": []}, {"fieldId": "descriptionAndLocationOfTheTumor", "label": "Opis i lokalizacja nowotworu", "errors": [], "type": "textarea-input-field", "disabled": false, "parentId": null, "value": "", "textAreaInputRows": 4, "maxTextLength": 1000, "placeholder": "", "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1m7j2Gzqrx1zGYEEeG97Ci2Nk--SMtwMIzm3RCVKeWBA/edit?tab=t.0#heading=h.vslv4z6b7mar", "tooltipWidth": 170}, {"fieldId": "primaryTumour", "label": "Guz pierwotny", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": true, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Głowa/szyja (bez węzłów chłonnych i skóry)", "value": "Głowa/szyja (bez węzłów chłonnych i skóry)"}, {"label": "Obwodowe węzły chłonne", "value": "Obwodowe węzły chłonne"}, {"label": "Śródpiersie", "value": "Śródpiersie"}, {"label": "Jama <PERSON> / prz<PERSON><PERSON><PERSON><PERSON> zaotrzewnowa", "value": "Jama <PERSON> / prz<PERSON><PERSON><PERSON><PERSON> zaotrzewnowa"}, {"label": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"label": "Skóra i/lub tkanki mi<PERSON>kie", "value": "Skóra i/lub tkanki mi<PERSON>kie"}, {"label": "<PERSON><PERSON>/nieo<PERSON>", "value": "<PERSON><PERSON>/nieo<PERSON>"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "fieldWidth": 400}, {"fieldId": "cnsInvolvement", "label": "Zajęcie OUN", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "cnsInvolvementCFS", "label": "Zajęcie płynu mózgowo-rdzeniowego (CFS+)", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "cnsInvolvement", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "cnsInvolvement"}]}, {"fieldId": "cnsInvolvementImaging", "label": "Zmiany w obrazowaniu OUN", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "cnsInvolvement", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "cnsInvolvement"}]}, {"fieldId": "cnsInvolvementCranialNervePalsy", "label": "Porażenie nerwu czaszkowego", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "cnsInvolvement", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "cnsInvolvement"}]}]}, {"fieldId": "boneMarrowInvolvement", "label": "Zajęcie szpiku kostnego", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "metastases", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "metastaticSites", "label": "Lokalizacja przerzutów", "errors": [], "type": "fields-set", "disabled": false, "parentId": "metastases", "fieldsSetItems": [{"fieldId": "metastaticSitesMediastinum", "label": "Śródpiersie", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "metastaticSitesLung", "label": "Płuco", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "metastaticSitesBones", "label": "<PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "metastaticSitesTesticles", "label": "<PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "metastaticSitesHeadNeck", "label": "Głowa/szyja", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "metastaticSitesPeripheralLymphNodes", "label": "Obwodowe węzły chłonne", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "metastaticSitesAbdominalCavity", "label": "<PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "metastaticSitesSkinSofTissues", "label": "Skóra i/lub tkanki mi<PERSON>kie", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "metastaticSitesSuperiorVenaCavaSyndrome", "label": "Zespół żyły głównej górnej", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "metastaticSitesEpiduralSpace", "label": "Przestrzeń zewnątrzoponowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "metastases"}]}]}, {"fieldId": "cancerStage", "label": "Stopień zaawansowania nowotworu zgodnie z klasyfikacjami TNM", "errors": [], "type": "fields-group", "disabled": false, "parentId": null, "fieldsGroupItems": [{"fieldId": "cancerStageClassificationsT", "label": "T", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "cancerStage", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": true, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "T0", "value": "T0"}, {"label": "T1", "value": "T1"}, {"label": "T1a", "value": "T1a"}, {"label": "T1b", "value": "T1b"}, {"label": "T2", "value": "T2"}, {"label": "T2a", "value": "T2a"}, {"label": "T2b", "value": "T2b"}, {"label": "TX", "value": "TX"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}, {"fieldId": "cancerStageClassificationsN", "label": "N", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "cancerStage", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": true, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "N0", "value": "N0"}, {"label": "N1", "value": "N1"}, {"label": "NX", "value": "NX"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}, {"fieldId": "cancerStageClassificationsM", "label": "M", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "cancerStage", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": true, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "M0", "value": "M0"}, {"label": "M1", "value": "M1"}, {"label": "MX", "value": "MX"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [], "fieldWidth": 545}, {"fieldId": "dateOfHistPatDiagnosis", "label": "Data diagnozy histopatologicznej", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": null, "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": []}, {"fieldId": "pathologicalDiagnosis", "label": "Rozpoznanie patomorfologiczne", "errors": [], "type": "ckeditor-input-field", "disabled": false, "parentId": null, "value": "", "maxTextLength": 1000, "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1m7j2Gzqrx1zGYEEeG97Ci2Nk--SMtwMIzm3RCVKeWBA/edit?tab=t.0#heading=h.9u349rljagbb", "tooltipWidth": 170}, {"fieldId": "clinicalStageOfCancer", "label": "Stadium kliniczne zaawansowania nowotworu", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "I", "value": "I"}, {"label": "II", "value": "II"}, {"label": "III", "value": "III"}, {"label": "IV", "value": "IV"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1m7j2Gzqrx1zGYEEeG97Ci2Nk--SMtwMIzm3RCVKeWBA/edit?tab=t.0#heading=h.czkho6lu9w2p", "tooltipWidth": 170}, {"fieldId": "riskGroup", "label": "Grupa ryzyka", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "SR", "value": "SR"}, {"label": "HR", "value": "HR"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1m7j2Gzqrx1zGYEEeG97Ci2Nk--SMtwMIzm3RCVKeWBA/edit?tab=t.0#heading=h.3fql01ziespn", "tooltipWidth": 170}, {"fieldId": "histPatDiagnosis", "label": "Diagnoza histopatologiczna", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Chłoniak limfoblastyczny T-komórkowy", "value": "Chłoniak limfoblastyczny T-komórkowy"}, {"label": "Chłoniak limfoblastyczny preB-komórkowy", "value": "Chłoniak limfoblastyczny preB-komórkowy"}, {"label": "Chłoniak/białaczka o mieszanym fenotypie", "value": "Chłoniak/białaczka o mieszanym fenotypie"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "fieldWidth": 400, "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1m7j2Gzqrx1zGYEEeG97Ci2Nk--SMtwMIzm3RCVKeWBA/edit?tab=t.0#heading=h.jqi5vxn2nez2", "tooltipWidth": 170}, {"fieldId": "immunohistochemistry", "label": "Immunohistochemia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "immunohistochemistry<PERSON><PERSON><PERSON>", "label": "", "errors": [], "type": "fields-group-table", "disabled": false, "parentId": "immunohistochemistry", "value": [{"immunohistochemistryMarkerName1": null, "immunohistochemistryMarkerResult1": null}], "fieldsGroupRowItems": [{"fieldId": "immunohistochemistryMarkerName1", "label": "<PERSON><PERSON>", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "immunohistochemistry<PERSON><PERSON><PERSON>", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "LBL-MARKER", "liveSelectItemsPerPage": 100, "placeholder": ""}, {"fieldId": "immunohistochemistryMarkerResult1", "label": "<PERSON><PERSON><PERSON>", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "immunohistochemistry<PERSON><PERSON><PERSON>", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "pos (+)", "value": "POS"}, {"label": "neg (-)", "value": "NEG"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "fieldsGroupTableUniqueValuesFromSelect": [], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "immunohistochemistry"}], "fieldWidth": 600}]}, {"fieldId": "icd10", "label": "ICD-10", "errors": [], "type": "live-select-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "ICD10", "liveSelectItemsPerPage": 100, "placeholder": "", "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1m7j2Gzqrx1zGYEEeG97Ci2Nk--SMtwMIzm3RCVKeWBA/edit?tab=t.0#heading=h.dlicra4yopsj", "tooltipWidth": 170}, {"fieldId": "iccc3", "label": "ICCC-3", "errors": [], "type": "live-select-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "ICCC", "liveSelectItemsPerPage": 100, "placeholder": "", "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1m7j2Gzqrx1zGYEEeG97Ci2Nk--SMtwMIzm3RCVKeWBA/edit?tab=t.0#heading=h.vif1jzl94lae", "tooltipWidth": 170}, {"fieldId": "comment", "label": "Komentarz", "errors": [], "type": "textarea-input-field", "disabled": false, "parentId": null, "value": "", "textAreaInputRows": 4, "maxTextLength": 1000, "placeholder": "", "renderConditions": []}]}