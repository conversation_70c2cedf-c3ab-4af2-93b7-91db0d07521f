{"formTitle": "", "sections": [{"title": "LECZENIE", "rootFieldsIds": ["treatmentStartDate", "treatmentGroup1", "chemotherapy", "radiotherapy", "surgery", "hsct", "hormoneTherapy", "immunotherapy", "targetedTherapy", "otherTherapy", "treatmentEndDate", "reasonForEndingTreatment"]}, {"title": "TOKSYCZNOŚCI", "rootFieldsIds": ["toxicity"]}, {"title": "ODPOWIEDŹ NA LECZENIE", "rootFieldsIds": ["patientStatus"]}, {"title": "KOMENTARZE", "rootFieldsIds": ["comment"]}], "fields": [{"fieldId": "treatmentStartDate", "label": "Data rozpoczęcia leczenia", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": null, "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": []}, {"fieldId": "treatmentGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "patientStatus", "fieldsGroupItems": [{"fieldId": "treatmentProtocol", "label": "<PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "treatmentGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "LBL-PROTOCOL", "liveSelectItemsPerPage": 100, "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "fieldWidth": 325, "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1m7j2Gzqrx1zGYEEeG97Ci2Nk--SMtwMIzm3RCVKeWBA/edit?tab=t.0#heading=h.rjt1q79tcsva", "tooltipWidth": 170}, {"fieldId": "chemotherapyTreatmentArm", "label": "<PERSON><PERSON><PERSON>", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "treatmentGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "LBL-THERAPY-ARM", "liveSelectItemsPerPage": 100, "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "fieldWidth": 325, "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1m7j2Gzqrx1zGYEEeG97Ci2Nk--SMtwMIzm3RCVKeWBA/edit?tab=t.0#heading=h.qu92e1leot0r", "tooltipWidth": 170}], "renderConditions": [], "fieldWidth": 650, "fieldsGroupColumns": 2}, {"fieldId": "chemotherapy", "label": "Chemioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "chemotherapyModificationOfDrugDoses", "label": "Modyfikacja dawek leków", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "chemotherapy", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}]}, {"fieldId": "chemotherapyBreakBetweenCycles14", "label": "Przerwa pomiędzy cyklami większa niż 14dni", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "chemotherapy", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}]}, {"fieldId": "chemotherapyNonAdministrationOfDrug", "label": "Niepodanie leku w jednej z faz leczenia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "chemotherapy", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}]}, {"fieldId": "chemotherapyPatientStatus", "label": "Status pacjenta w 33 dniu", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "chemotherapy", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "CR", "value": "CR"}, {"label": "PR", "value": "PR"}, {"label": "NR", "value": "NR"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}]}, {"fieldId": "chemotherapyResidualTumor", "label": "Guz resztkowy", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "chemotherapy", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}], "children": [{"fieldId": "residualTumorVolume", "label": "Zmniejszenie początkowej objętości guza o co najmniej 35%", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "chemotherapyResidualTumor", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapyResidualTumor"}]}, {"fieldId": "residualTumorPresenceOfCancerCells", "label": "Obe<PERSON><PERSON><PERSON>ć komórek <PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "chemotherapyResidualTumor", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapyResidualTumor"}]}, {"fieldId": "residualTumorResection", "label": "Resekcja guza resztkowego", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "chemotherapyResidualTumor", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapyResidualTumor"}]}]}, {"fieldId": "chemotherapyTreatmentArmChange", "label": "Zmiana ramienia terapeutycznego", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "chemotherapy", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}], "children": [{"fieldId": "treatmentArmChangeNew", "label": "Nowe ramię terapeutyczne", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "treatmentGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "LBL-THERAPY-ARM", "liveSelectItemsPerPage": 100, "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapyTreatmentArmChange"}], "fieldWidth": 325}]}]}, {"fieldId": "radiotherapy", "label": "Radioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "surgery", "label": "Chirurgia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "hsct", "label": "Przeszczep", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "hormoneTherapy", "label": "Hormonoterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "immunotherapy", "label": "Immunoterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "targetedTherapy", "label": "<PERSON><PERSON><PERSON> celowana", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "otherTherapy", "label": "<PERSON><PERSON> metody <PERSON>", "errors": [], "type": "text-input-field", "disabled": false, "parentId": null, "value": "", "maxTextLength": 255, "placeholder": "", "renderConditions": []}, {"fieldId": "treatmentEndDate", "label": "Data zakończenia leczenia", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": null, "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": false, "renderConditions": []}, {"fieldId": "reasonForEndingTreatment", "label": "Przyczyna zakończenia leczenia", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Wyleczenie", "value": "PZL1"}, {"label": "Toksyczności i działania niepożądane", "value": "PZL2"}, {"label": "Wyczerpanie możliwości terapeutycznych", "value": "PZL3"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "PZL4"}, {"label": "Decyzja rodzica", "value": "PZL5"}, {"label": "Zgon", "value": "PZL6"}, {"label": "<PERSON><PERSON>", "value": "PZL7"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "fieldWidth": 400}, {"fieldId": "toxicity", "label": "Toksyczności leczenia", "errors": [], "type": "fields-set", "disabled": false, "parentId": null, "fieldsSetItems": [{"fieldId": "toxicityWBC", "label": "WBC", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityGranulocyte", "label": "Granulocyty", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityHaemoglobin", "label": "Hemoglobina", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityThrombocytes", "label": "Płytki krwi", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityHepato", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityNefro", "label": "Nefrotok<PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityCardio", "label": "Kardioto<PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityEnteritis", "label": "Zapalenie j<PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityNeuro", "label": "Neurotoks<PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityPulmo", "label": "Pul<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityInfection", "label": "Infekcje", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityAnaphylacticReaction", "label": "Reakcja anafilaktyczna", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}], "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1m7j2Gzqrx1zGYEEeG97Ci2Nk--SMtwMIzm3RCVKeWBA/edit?tab=t.0#heading=h.hns4l24c3mb0", "tooltipWidth": 170}, {"fieldId": "patientStatus", "label": "Status pacjenta", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "SD", "value": "SD"}, {"label": "CR", "value": "CR"}, {"label": "Progresja", "value": "progression"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "children": [{"fieldId": "sdDate", "label": "Data potwierdzenia stabilnej choroby", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "patientStatus", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "SD", "conditionType": "equal", "dependentFieldId": "patientStatus"}]}, {"fieldId": "crDate", "label": "Data remisji całkowitej", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "patientStatus", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "CR", "conditionType": "equal", "dependentFieldId": "patientStatus"}]}, {"fieldId": "crCycle", "label": "Po którym cyklu chemioterapii", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "patientStatus", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Ia", "value": "Ia"}, {"label": "Ib", "value": "Ib"}, {"label": "M", "value": "M"}, {"label": "IIa", "value": "IIa"}, {"label": "IIb", "value": "IIb"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "CR", "conditionType": "equal", "dependentFieldId": "patientStatus"}]}, {"fieldId": "progressionGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "patientStatus", "fieldsGroupItems": [{"fieldId": "progressionDate", "label": "Data progresji", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "patientStatus", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "progressionType", "label": "<PERSON><PERSON> prog<PERSON>", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "patientStatus", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Lokalna", "value": "Lokalna"}, {"label": "Odległa", "value": "Odległa"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "progression", "conditionType": "equal", "dependentFieldId": "patientStatus"}], "fieldWidth": 580, "fieldsGroupColumns": 2}, {"fieldId": "progressionLocalizationSet", "label": "", "errors": [], "type": "fields-set", "disabled": false, "parentId": "patientStatus", "fieldsSetItems": [{"fieldId": "progressionLocalizationPrimaryLesions", "label": "Guz pierwotny", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "progressionLocalizationSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "progressionLocalizationNonPrimaryLesions", "label": "Poza guzem pierwotnym", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "progressionLocalizationSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "progressionLocalizationCns", "label": "OUN", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "progressionLocalizationSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "progressionLocalizationBm", "label": "Szpik kostny", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "progressionLocalizationSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "progressionLocalizationBone", "label": "<PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "progressionLocalizationSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}], "renderConditions": [{"conditionalValue": "progression", "conditionType": "equal", "dependentFieldId": "patientStatus"}]}, {"fieldId": "progressionProtocol", "label": "Protokół leczenia progresji", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "patientStatus", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "LBL-SECOND-LINE", "liveSelectItemsPerPage": 100, "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "progression", "conditionType": "equal", "dependentFieldId": "patientStatus"}], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1m7j2Gzqrx1zGYEEeG97Ci2Nk--SMtwMIzm3RCVKeWBA/edit?tab=t.0#heading=h.3dmtn9h4mtip", "tooltipWidth": 170}, {"fieldId": "progressionGroup2", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "patientStatus", "fieldsGroupItems": [{"fieldId": "progressionEndTreatmentDate", "label": "Data zakończenia leczenia progresji", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "patientStatus", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "progressionEndTreatmentStatus", "label": "Status po leczeniu progresji", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "patientStatus", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "CR", "value": "CR"}, {"label": "Progresja", "value": "Progresja"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "progression", "conditionType": "equal", "dependentFieldId": "patientStatus"}], "fieldWidth": 580, "fieldsGroupColumns": 2}]}, {"fieldId": "comment", "label": "", "errors": [], "type": "textarea-input-field", "disabled": false, "parentId": null, "value": "", "textAreaInputRows": 4, "maxTextLength": 1000, "placeholder": "", "renderConditions": []}]}