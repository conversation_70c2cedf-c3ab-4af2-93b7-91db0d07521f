{"formTitle": "Diag<PERSON>za", "sections": [{"title": "CHOROBY WSPÓŁISTNIEJĄCE", "rootFieldsIds": ["predispositionToCancer", "cancerInTheFamily", "otherComorbiditiesOrTherapies", "assessmentOfThePatientsCondition"]}, {"title": "OPIS NOWOTWORU", "rootFieldsIds": ["dateOfDiagnosis", "descriptionAndLocationOfTheTumor", "eaySet", "formOfDisease", "liquidBiopsy", "familyType", "rb1Mutation", "trilateralRetinoblastoma", "metastaticSites", "cancerStage"]}, {"title": "DIAGNOZA HISTOPATOLOGICZNA", "rootFieldsIds": ["pathologicalDiagnosis", "clinicalStageOfCancer", "rightEyeICRB", "leftEyeICRB"]}, {"title": "KLASYFIKACJA", "rootFieldsIds": ["icd10", "iccc3"]}, {"title": "KOMENTARZE", "rootFieldsIds": ["comment"]}], "fields": [{"fieldId": "predispositionToCancer", "label": "Predyspozycje do nowotworzenia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "cancerInTheFamily", "label": "Choroba nowotworowa w rodzinie", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "otherComorbiditiesOrTherapies", "label": "Inne choroby współistniejące lub terapie", "errors": [], "type": "textarea-input-field", "disabled": false, "parentId": null, "value": "", "textAreaInputRows": 4, "maxTextLength": 1000, "placeholder": "", "renderConditions": []}, {"fieldId": "assessmentOfThePatientsCondition", "label": "<PERSON><PERSON>na stanu pacjenta wg s<PERSON><PERSON>/<PERSON>nsky", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "0", "value": "0"}, {"label": "10", "value": "10"}, {"label": "20", "value": "20"}, {"label": "30", "value": "30"}, {"label": "30", "value": "30"}, {"label": "40", "value": "40"}, {"label": "50", "value": "50"}, {"label": "60", "value": "60"}, {"label": "70", "value": "70"}, {"label": "80", "value": "80"}, {"label": "90", "value": "90"}, {"label": "100", "value": "100"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": []}, {"fieldId": "dateOfDiagnosis", "label": "Data rozpoznania", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": null, "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": []}, {"fieldId": "descriptionAndLocationOfTheTumor", "label": "Opis i lokalizacja nowotworu", "errors": [], "type": "textarea-input-field", "disabled": false, "parentId": null, "value": "", "textAreaInputRows": 4, "maxTextLength": 1000, "placeholder": "", "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1WZCYg5KdN2GTp9k40Rix7RIXdUpYJc86EChsKPDyDDg/edit?tab=t.0#heading=h.vslv4z6b7mar", "tooltipWidth": 170}, {"fieldId": "eaySet", "label": "", "errors": [], "type": "fields-set", "disabled": false, "parentId": null, "fieldsSetItems": [{"fieldId": "leukoria", "label": "<PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "eaySet", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "<PERSON>o prawe", "value": "<PERSON>o prawe"}, {"label": "<PERSON>o lewe", "value": "<PERSON>o lewe"}, {"label": "Oko prawe i lewe", "value": "Oko prawe i lewe"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": []}, {"fieldId": "squint", "label": "<PERSON><PERSON>", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "eaySet", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "<PERSON>o prawe", "value": "<PERSON>o prawe"}, {"label": "<PERSON>o lewe", "value": "<PERSON>o lewe"}, {"label": "Oko prawe i lewe", "value": "Oko prawe i lewe"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": []}, {"fieldId": "pseudoInflammatorySymptoms", "label": "<PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "eaySet", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "<PERSON>o prawe", "value": "<PERSON>o prawe"}, {"label": "<PERSON>o lewe", "value": "<PERSON>o lewe"}, {"label": "Oko prawe i lewe", "value": "Oko prawe i lewe"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": []}], "renderConditions": []}, {"fieldId": "formOfDisease", "label": "<PERSON><PERSON><PERSON> choroby", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Jednooczna oko prawe", "value": "Jednooczna oko prawe"}, {"label": "Jednoczona oko lewe", "value": "Jednoczona oko lewe"}, {"label": "Obuoczna", "value": "Obuoczna"}, {"label": "Pozagałkowa", "value": "Pozagałkowa"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "children": [{"fieldId": "retrobulbarLocation", "label": "Lokalizacja pozagałkowa", "errors": [], "type": "text-input-field", "disabled": false, "parentId": "formOfDisease", "value": "", "maxTextLength": 255, "placeholder": "", "renderConditions": [{"conditionalValue": "Pozagałkowa", "conditionType": "equal", "dependentFieldId": "formOfDisease"}], "fieldWidth": 500}]}, {"fieldId": "liquidBiopsy", "label": "Płynna biopsja", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "familyType", "label": "<PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "rb1Mutation", "label": "Mutacja RB1 / <PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "trilateralRetinoblastoma", "label": "Siatkówczak trójstronny", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "metastaticSites", "label": "Lokalizacja przerzutów", "errors": [], "type": "fields-set", "disabled": false, "parentId": null, "fieldsSetItems": [{"fieldId": "metastaticSitesOUN", "label": "OUN", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "metastaticSitesEyeSocket", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "metastaticSitesOpticNerve", "label": "Naciek nerwu wzrokowego", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "metastaticSitesEyeballWall", "label": "Ściana gałki ocznej", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "metastaticSitesLeptomeningeal", "label": "Rozsiew oponowy", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "metastaticSitesDistantMetastases", "label": "Przerzuty odległe", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}], "renderConditions": []}, {"fieldId": "cancerStage", "label": "Stopień zaawansowania nowotworu zgodnie z klasyfikacjami TNM", "errors": [], "type": "fields-group", "disabled": false, "parentId": null, "fieldsGroupItems": [{"fieldId": "cancerStageClassificationsT", "label": "T", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "cancerStage", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": true, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "T0", "value": "T0"}, {"label": "T1", "value": "T1"}, {"label": "T1a", "value": "T1a"}, {"label": "T1b", "value": "T1b"}, {"label": "T2", "value": "T2"}, {"label": "T2a", "value": "T2a"}, {"label": "T2b", "value": "T2b"}, {"label": "TX", "value": "TX"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}, {"fieldId": "cancerStageClassificationsN", "label": "N", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "cancerStage", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": true, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "N0", "value": "N0"}, {"label": "N1", "value": "N1"}, {"label": "NX", "value": "NX"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}, {"fieldId": "cancerStageClassificationsM", "label": "M", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "cancerStage", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": true, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "M0", "value": "M0"}, {"label": "M1", "value": "M1"}, {"label": "MX", "value": "MX"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [], "fieldWidth": 545}, {"fieldId": "pathologicalDiagnosis", "label": "Rozpoznanie patomorfologiczne", "errors": [], "type": "ckeditor-input-field", "disabled": false, "parentId": null, "value": "", "maxTextLength": 1000, "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1WZCYg5KdN2GTp9k40Rix7RIXdUpYJc86EChsKPDyDDg/edit?tab=t.0#heading=h.9u349rljagbb", "tooltipWidth": 170}, {"fieldId": "clinicalStageOfCancer", "label": "Stadium kliniczne zaawansowania nowotworu", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "I", "value": "I"}, {"label": "II", "value": "II"}, {"label": "III", "value": "III"}, {"label": "IV", "value": "IV"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1WZCYg5KdN2GTp9k40Rix7RIXdUpYJc86EChsKPDyDDg/edit?tab=t.0#heading=h.xxiv9zvjcdbq", "tooltipWidth": 170}, {"fieldId": "rightEyeICRB", "label": "<PERSON><PERSON> prawe (Stadium ICRB)", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "nie dot<PERSON>y", "value": "nie dot<PERSON>y"}, {"label": "A", "value": "A"}, {"label": "B", "value": "B"}, {"label": "C", "value": "C"}, {"label": "D", "value": "D"}, {"label": "E", "value": "E"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1WZCYg5KdN2GTp9k40Rix7RIXdUpYJc86EChsKPDyDDg/edit?tab=t.0#heading=h.bdvpipims54", "tooltipWidth": 170}, {"fieldId": "leftEyeICRB", "label": "<PERSON><PERSON> lewe (Stadium ICRB)", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "nie dot<PERSON>y", "value": "nie dot<PERSON>y"}, {"label": "A", "value": "A"}, {"label": "B", "value": "B"}, {"label": "C", "value": "C"}, {"label": "D", "value": "D"}, {"label": "E", "value": "E"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1WZCYg5KdN2GTp9k40Rix7RIXdUpYJc86EChsKPDyDDg/edit?tab=t.0#heading=h.bdvpipims54", "tooltipWidth": 170}, {"fieldId": "icd10", "label": "ICD-10", "errors": [], "type": "live-select-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "ICD10", "liveSelectItemsPerPage": 100, "placeholder": "", "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1WZCYg5KdN2GTp9k40Rix7RIXdUpYJc86EChsKPDyDDg/edit?tab=t.0#heading=h.dlicra4yopsj", "tooltipWidth": 170}, {"fieldId": "iccc3", "label": "ICCC-3", "errors": [], "type": "live-select-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "ICCC", "liveSelectItemsPerPage": 100, "placeholder": "", "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1WZCYg5KdN2GTp9k40Rix7RIXdUpYJc86EChsKPDyDDg/edit?tab=t.0#heading=h.vif1jzl94lae", "tooltipWidth": 170}, {"fieldId": "comment", "label": "", "errors": [], "type": "textarea-input-field", "disabled": false, "parentId": null, "value": "", "textAreaInputRows": 4, "maxTextLength": 1000, "placeholder": "", "renderConditions": []}]}