{"formTitle": "", "sections": [{"title": "LECZENIE", "rootFieldsIds": ["treatmentStartDate", "treatmentProtocol", "chemotherapy", "radiotherapy", "surgery", "hsct", "hormoneTherapy", "immunotherapy", "targetedTherapy", "otherTherapy", "spinalCompressionTreatment", "treatmentEndDate", "reasonForEndingTreatment"]}, {"title": "TOKSYCZNOŚCI", "rootFieldsIds": ["toxicity"]}, {"title": "ODPOWIEDŹ NA LECZENIE", "rootFieldsIds": ["patientStatus"]}, {"title": "KOMENTARZE", "rootFieldsIds": ["comment"]}], "fields": [{"fieldId": "treatmentStartDate", "label": "Data rozpoczęcia leczenia", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": null, "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": []}, {"fieldId": "treatmentProtocol", "label": "<PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "live-select-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "NBL-PROTOCOL", "liveSelectItemsPerPage": 100, "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "fieldWidth": 300, "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1mKrdm1voQTJsf_dCMZt6vzeh-iHf49qiMpkK2Lx_D6A/edit?tab=t.0#heading=h.ghz84lmcl3qy", "tooltipWidth": 170}, {"fieldId": "chemotherapy", "label": "Chemioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "chemotherapyStartDate", "label": "Data rozpoczęcia chemioterapii", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "chemotherapy", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}]}, {"fieldId": "chemotherapyGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "chemotherapy", "fieldsGroupItems": [{"fieldId": "chemotherapyType", "label": "<PERSON><PERSON><PERSON>emioterapi<PERSON>", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "chemotherapyGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "NBL-CHEM-01", "liveSelectItemsPerPage": 100, "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1mKrdm1voQTJsf_dCMZt6vzeh-iHf49qiMpkK2Lx_D6A/edit?tab=t.0#heading=h.eha3r7c3ctn", "tooltipWidth": 170}, {"fieldId": "chemotherapyNumberOfCycles", "label": "Liczba cykli", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "chemotherapyGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "1", "value": "1"}, {"label": "2", "value": "2"}, {"label": "3", "value": "3"}, {"label": "4", "value": "4"}, {"label": "5", "value": "5"}, {"label": "6", "value": "6"}, {"label": "7", "value": "7"}, {"label": "8", "value": "8"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}], "fieldsGroupColumns": 2, "fieldWidth": 500}, {"fieldId": "chemotherapyInitialResponse", "label": "Ocena odpowiedzi na leczenie wstępne (indukcyjne)", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "chemotherapy", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Remisja całkowita", "value": "Remisja całkowita"}, {"label": "Bardzo dobra odpowiedź częściowa", "value": "Bardzo dobra odpowiedź częściowa"}, {"label": "Odpowiedź częściowa", "value": "Odpowiedź częściowa"}, {"label": "Odpowied<PERSON> mieszana", "value": "Odpowied<PERSON> mieszana"}, {"label": "Brak odpowiedzi na leczenie", "value": "Brak odpowiedzi na leczenie"}, {"label": "Progresja choroby", "value": "Progresja choroby"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}], "fieldWidth": 400}, {"fieldId": "chemotherapyOther", "label": "Inna/dodatkowa chemioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "chemotherapy", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}]}, {"fieldId": "chemotherapyEndDate", "label": "Data zakończenia chemioterapii", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "chemotherapy", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}]}]}, {"fieldId": "radiotherapy", "label": "Radioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "radiotherapyStartDate", "label": "Data rozpoczęcia", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "radiotherapy", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "radiotherapy"}]}, {"fieldId": "radiotherapyGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "radiotherapy", "fieldsGroupItems": [{"fieldId": "radiotherapyDose", "label": "Dawka", "errors": [], "type": "text-input-field", "disabled": false, "parentId": "radiotherapyGroup1", "value": "", "maxTextLength": 1000, "placeholder": ""}, {"fieldId": "radiotherapyLocation", "label": "Lokalizacja napromieniania", "errors": [], "type": "text-input-field", "disabled": false, "parentId": "radiotherapyGroup1", "value": "", "maxTextLength": 1000, "placeholder": ""}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "radiotherapy"}], "fieldsGroupColumns": 2, "fieldWidth": 1000}, {"fieldId": "radiotherapyEndDate", "label": "Data zakończenia", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "radiotherapy", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "radiotherapy"}]}]}, {"fieldId": "surgery", "label": "Chirurgia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "surgeryPrimary", "label": "<PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "surgery", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "surgery"}], "children": [{"fieldId": "surgeryPrimaryGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "surgeryPrimary", "fieldsGroupItems": [{"fieldId": "surgeryPrimaryDate", "label": "Data zabiegu", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "surgeryPrimaryGroup1", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "surgeryPrimaryLocation", "label": "Lokalizacja zabiegu", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "surgeryPrimaryGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}, {"label": "Miednica", "value": "Miednica"}, {"label": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"label": "Szyja", "value": "Szyja"}, {"label": "Ok<PERSON>łordzeniowa", "value": "Ok<PERSON>łordzeniowa"}, {"label": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}, {"fieldId": "surgeryPrimaryResectionType", "label": "<PERSON><PERSON>", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "surgeryPrimaryGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Całkowita", "value": "Całkowita"}, {"label": "Niecałkowita", "value": "Niecałkowita"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "surgery"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "surgeryPrimary"}], "fieldsGroupColumns": 3, "fieldWidth": 600}]}, {"fieldId": "surgeryPostponed", "label": "<PERSON><PERSON>eg o<PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "surgery", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "surgery"}], "children": [{"fieldId": "surgeryPostponedGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "surgeryPostponed", "fieldsGroupItems": [{"fieldId": "surgeryPostponedDate", "label": "Data zabiegu", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "surgeryPostponedGroup1", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "surgeryPostponedLocation", "label": "Lokalizacja zabiegu", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "surgeryPostponedGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}, {"label": "Miednica", "value": "Miednica"}, {"label": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"label": "Szyja", "value": "Szyja"}, {"label": "Ok<PERSON>łordzeniowa", "value": "Ok<PERSON>łordzeniowa"}, {"label": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}, {"fieldId": "surgeryPostponedResectionType", "label": "<PERSON><PERSON>", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "surgeryPostponedGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Całkowita", "value": "Całkowita"}, {"label": "Niecałkowita", "value": "Niecałkowita"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "surgery"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "surgeryPostponed"}], "fieldsGroupColumns": 3, "fieldWidth": 600}]}]}, {"fieldId": "hsct", "label": "Przeszczep", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "hsctDate", "label": "Data przeszczepu", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "hsct", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "hsct"}]}]}, {"fieldId": "hormoneTherapy", "label": "Hormonoterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "immunotherapy", "label": "Immunoterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "immunotherapyGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "immunotherapy", "fieldsGroupItems": [{"fieldId": "immunotherapyAntyGD2StartDate", "label": "Data rozpoczęcia", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "immunotherapyGroup1", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "immunotherapyAntyGD2EndDate", "label": "Data zakończenia", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "immunotherapyGroup1", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "immunotherapyAntyGD2NumberOfCycles", "label": "Liczba cykli", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "immunotherapyGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "1", "value": "1"}, {"label": "2", "value": "2"}, {"label": "3", "value": "3"}, {"label": "4", "value": "4"}, {"label": "5", "value": "5"}, {"label": "6", "value": "6"}, {"label": "7", "value": "7"}, {"label": "8", "value": "8"}, {"label": "9", "value": "9"}, {"label": "10", "value": "10"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "immunotherapy", "isInherited": true}], "fieldsGroupColumns": 3, "fieldWidth": 600}]}, {"fieldId": "targetedTherapy", "label": "<PERSON><PERSON><PERSON> celowana", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "otherTherapy", "label": "<PERSON><PERSON> metody <PERSON>", "errors": [], "type": "text-input-field", "disabled": false, "parentId": null, "value": "", "maxTextLength": 1000, "placeholder": "", "renderConditions": [], "fieldWidth": 500}, {"fieldId": "spinalCompressionTreatment", "label": "Leczenie ze względu na ucisk rdzenia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "spinalCompressionTreatmentChemotherapy", "label": "Chemioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "spinalCompressionTreatment", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "spinalCompressionTreatment"}], "children": [{"fieldId": "spinalCompressionTreatmentChemotherapyType", "label": "<PERSON><PERSON><PERSON>emioterapi<PERSON>", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "spinalCompressionTreatmentChemotherapy", "value": null, "canCleanSelect": false, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "NBL-CHEM-01", "liveSelectItemsPerPage": 100, "placeholder": "", "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "spinalCompressionTreatment"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "spinalCompressionTreatmentChemotherapy"}], "fieldWidth": 300}]}, {"fieldId": "spinalCompressionTreatmentSurgery", "label": "Chirurgia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "spinalCompressionTreatment", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "spinalCompressionTreatment"}]}, {"fieldId": "spinalCompressionTreatmentRadiotherapy", "label": "Radioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "spinalCompressionTreatment", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "spinalCompressionTreatment"}], "children": [{"fieldId": "spinalCompressionTreatmentRadiotherapyGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "spinalCompressionTreatmentRadiotherapy", "fieldsGroupItems": [{"fieldId": "spinalCompressionTreatmentRadiotherapyDose", "label": "Dawka", "errors": [], "type": "text-input-field", "disabled": false, "parentId": "spinalCompressionTreatmentRadiotherapyGroup1", "value": "", "maxTextLength": 1000, "placeholder": ""}, {"fieldId": "spinalCompressionTreatmentRadiotherapyyLocation", "label": "Lokalizacja napromieniania", "errors": [], "type": "text-input-field", "disabled": false, "parentId": "spinalCompressionTreatmentRadiotherapyGroup1", "value": "", "maxTextLength": 1000, "placeholder": ""}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "spinalCompressionTreatment"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "spinalCompressionTreatmentRadiotherapy"}], "fieldsGroupColumns": 2, "fieldWidth": 600}]}]}, {"fieldId": "treatmentEndDate", "label": "Data zakończenia leczenia", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": null, "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": false, "renderConditions": []}, {"fieldId": "reasonForEndingTreatment", "label": "Przyczyna zakończenia leczenia", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Wyleczenie", "value": "PZL1"}, {"label": "Toksyczności i działania niepożądane", "value": "PZL2"}, {"label": "Wyczerpanie możliwości terapeutycznych", "value": "PZL3"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "PZL4"}, {"label": "Decyzja rodzica", "value": "PZL5"}, {"label": "Zgon", "value": "PZL6"}, {"label": "<PERSON><PERSON>", "value": "PZL7"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "fieldWidth": 400}, {"fieldId": "toxicity", "label": "Toksyczności leczenia", "errors": [], "type": "fields-set", "disabled": false, "parentId": null, "fieldsSetItems": [{"fieldId": "toxicityWBC", "label": "WBC", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityGranulocyte", "label": "Granulocyty", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityHaemoglobin", "label": "Hemoglobina", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityThrombocytes", "label": "Płytki krwi", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityHepato", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityNefro", "label": "Nefrotok<PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityCardio", "label": "Kardioto<PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityEnteritis", "label": "Zapalenie j<PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityNeuro", "label": "Neurotoks<PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityPulmo", "label": "Pul<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityInfection", "label": "Infekcje", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}, {"fieldId": "toxicityAnaphylacticReaction", "label": "Reakcja anafilaktyczna", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "toxicity", "value": "not-selected", "threeStateLevelOptions": [{"label": "3", "value": "3"}, {"label": "", "value": "not-selected"}, {"label": "4", "value": "4"}], "renderConditions": []}], "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1mKrdm1voQTJsf_dCMZt6vzeh-iHf49qiMpkK2Lx_D6A/edit?tab=t.0#heading=h.hns4l24c3mb0", "tooltipWidth": 170}, {"fieldId": "patientStatus", "label": "Status pacjenta", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "SD", "value": "SD"}, {"label": "CR", "value": "CR"}, {"label": "Progresja", "value": "progression"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "children": [{"fieldId": "sdDate", "label": "Data potwierdzenia stabilnej choroby", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "patientStatus", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "SD", "conditionType": "equal", "dependentFieldId": "patientStatus"}]}, {"fieldId": "crDate", "label": "Data remisji całkowitej", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "patientStatus", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "CR", "conditionType": "equal", "dependentFieldId": "patientStatus"}]}, {"fieldId": "progressionGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "patientStatus", "fieldsGroupItems": [{"fieldId": "progressionDate", "label": "Data progresji", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "progressionGroup1", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "progressionType", "label": "<PERSON><PERSON> prog<PERSON>", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "progressionGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Lokalna", "value": "Lokalna"}, {"label": "Odległa", "value": "Odległa"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "progression", "conditionType": "equal", "dependentFieldId": "patientStatus"}], "fieldWidth": 580, "fieldsGroupColumns": 2}, {"fieldId": "progressionChemotherapy", "label": "Chemioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "patientStatus", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "progression", "conditionType": "equal", "dependentFieldId": "patientStatus"}], "children": [{"fieldId": "progressionChemotherapyType", "label": "<PERSON><PERSON><PERSON>emioterapi<PERSON>", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "progressionChemotherapy", "value": null, "canCleanSelect": false, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "NBL-CHEM-01", "liveSelectItemsPerPage": 100, "placeholder": "", "renderConditions": [{"conditionalValue": "progression", "conditionType": "equal", "dependentFieldId": "patientStatus"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "progressionChemotherapy"}], "fieldWidth": 300}]}, {"fieldId": "progressionRadiotherapy", "label": "Radioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "patientStatus", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "progression", "conditionType": "equal", "dependentFieldId": "patientStatus"}], "children": [{"fieldId": "progressionRadiotherapyGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "progressionRadiotherapy", "fieldsGroupItems": [{"fieldId": "progressionRadiotherapyDose", "label": "Dawka", "errors": [], "type": "text-input-field", "disabled": false, "parentId": "progressionRadiotherapyGroup1", "value": "", "maxTextLength": 1000, "placeholder": "", "fieldWidth": 500}, {"fieldId": "progressionRadiotherapyLocation", "label": "Lokalizacja napromieniania", "errors": [], "type": "text-input-field", "disabled": false, "parentId": "progressionRadiotherapyGroup1", "value": "", "maxTextLength": 1000, "placeholder": "", "fieldWidth": 500}], "renderConditions": [{"conditionalValue": "progression", "conditionType": "equal", "dependentFieldId": "patientStatus"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "progressionRadiotherapy"}], "fieldsGroupColumns": 2, "fieldWidth": 1000}]}, {"fieldId": "progressionSurgery", "label": "Chirurgia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "patientStatus", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "progression", "conditionType": "equal", "dependentFieldId": "patientStatus"}]}, {"fieldId": "progressionHsct", "label": "Przeszczep", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "patientStatus", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "progression", "conditionType": "equal", "dependentFieldId": "patientStatus"}], "children": [{"fieldId": "progressionHsctGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "progressionHsct", "fieldsGroupItems": [{"fieldId": "progressionHsctDate", "label": "Data przeszczepu", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "progressionHsctGroup1", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "progressionHsctType", "label": "Typ przeszczepu", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "progressionHsctGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Auto", "value": "Auto"}, {"label": "Allo", "value": "Allo"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "progression", "conditionType": "equal", "dependentFieldId": "patientStatus"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "progressionHsct"}], "fieldsGroupColumns": 2, "fieldWidth": 600}]}, {"fieldId": "progressionGroup2", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "patientStatus", "fieldsGroupItems": [{"fieldId": "progressionEndTreatmentDate", "label": "Data zakończenia leczenia progresji", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "progressionGroup2", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true}, {"fieldId": "progressionEndTreatmentStatus", "label": "Status po leczeniu progresji", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "progressionGroup2", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "CR", "value": "CR"}, {"label": "Progresja", "value": "Progresja"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [{"conditionalValue": "progression", "conditionType": "equal", "dependentFieldId": "patientStatus"}], "fieldWidth": 580, "fieldsGroupColumns": 2}], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/1mKrdm1voQTJsf_dCMZt6vzeh-iHf49qiMpkK2Lx_D6A/edit?tab=t.0#heading=h.egx5po1d0zd2", "tooltipWidth": 170}, {"fieldId": "comment", "label": "", "errors": [], "type": "textarea-input-field", "disabled": false, "parentId": null, "value": "", "textAreaInputRows": 4, "maxTextLength": 1000, "placeholder": "", "renderConditions": []}]}