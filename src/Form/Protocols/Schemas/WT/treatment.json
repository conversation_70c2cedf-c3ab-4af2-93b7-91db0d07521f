{"formTitle": "", "sections": [{"title": "LECZENIE", "rootFieldsIds": ["treatmentStartDate", "treatmentProtocol", "patientNumber", "chemotherapy", "radiotherapy", "surgery", "hsct", "hormoneTherapy", "immunotherapy", "targetedTherapy", "otherTherapy", "treatmentEndDate", "reasonForEndingTreatment"]}, {"title": "ODPOWIEDŹ NA LECZENIE", "rootFieldsIds": ["toxicity", "responseMetastases", "patientStatus"]}, {"title": "KOMENTARZE", "rootFieldsIds": ["comment"]}], "fields": [{"fieldId": "treatmentStartDate", "label": "Data rozpoczęcia leczenia", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": null, "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": []}, {"fieldId": "treatmentProtocol", "label": "<PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "live-select-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "WT-PROTOCOL", "liveSelectItemsPerPage": 100, "placeholder": "", "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/16WpKKPboH4KfIvL2TdX2_PGLz430fZIR1FrpB7Gbnp4/edit?tab=t.0#heading=h.rjt1q79tcsva", "tooltipWidth": 170}, {"fieldId": "patientNumber", "label": "Numer pacjenta w Umbrella SIOP RTSG 2016 (wypełnia koordynator)", "errors": [], "type": "text-input-field", "disabled": false, "parentId": null, "value": "", "maxTextLength": 1000, "placeholder": "", "renderConditions": [], "fieldWidth": 500}, {"fieldId": "chemotherapy", "label": "Chemioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "chemotherapyPreSurgery", "label": "Chemioterapia przedoperacyjna", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "chemotherapy", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}], "children": [{"fieldId": "preSurgeryTherapeuticArm", "label": "<PERSON><PERSON><PERSON>", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "chemotherapyPreSurgery", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": true, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Vincristine only", "value": "Vincristine only"}, {"label": "AV as per protocol (4 weeks)", "value": "AV as per protocol (4 weeks)"}, {"label": "AV plus one extra vincristine", "value": "AV plus one extra vincristine"}, {"label": "AVD as per protocol", "value": "AVD as per protocol"}, {"label": "AVD plus one extra vincristine", "value": "AVD plus one extra vincristine"}, {"label": "AV as per protocol for stage V (6 weeks)", "value": "AV as per protocol for stage V (6 weeks)"}, {"label": "VP16 / Carboplatin (6 weeks)", "value": "VP16 / Carboplatin (6 weeks)"}, {"label": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapyPreSurgery"}], "fieldWidth": 390, "children": [{"fieldId": "preSurgeryTherpaeuticArmChange", "label": "Inne ramię terapeutyczne lub modyfikacja ramienia terapeutycznego", "errors": [], "type": "text-input-field", "disabled": false, "parentId": "preSurgeryTherapeuticArm", "value": "", "maxTextLength": 255, "placeholder": "", "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapyPreSurgery"}, {"conditionalValue": "<PERSON><PERSON>", "conditionType": "equal", "dependentFieldId": "preSurgeryTherapeuticArm"}], "fieldWidth": 390}], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/16WpKKPboH4KfIvL2TdX2_PGLz430fZIR1FrpB7Gbnp4/edit?tab=t.0#heading=h.t6ctmdsi34ki", "tooltipWidth": 170}, {"fieldId": "preSurgeryToxicity", "label": "Toksyczność w trakcie chemioterapii przedoperacyjnej", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "chemotherapyPreSurgery", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapyPreSurgery"}], "children": [{"fieldId": "preSurgeryToxicitySet", "label": "", "errors": [], "type": "fields-set", "disabled": false, "parentId": "preSurgeryToxicity", "fieldsSetItems": [{"fieldId": "preSyrgeryToxicityFebrileNeutropenia", "label": "Gorączka neutropeniczna", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "preSurgeryToxicitySet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy", "isInherited": true}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapyPreSurgery", "isInherited": true}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "preSurgeryToxicity", "isInherited": true}], "fieldLabelWidth": 400}, {"fieldId": "preSurgeryToxicitySepsis", "label": "Sepsa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "preSurgeryToxicitySet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy", "isInherited": true}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapyPreSurgery", "isInherited": true}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "preSurgeryToxicity", "isInherited": true}], "fieldLabelWidth": 400}, {"fieldId": "preSurgeryToxicityLeftVentricularSystolicDysfunction", "label": "Dysfunkcja skurczowa lewej komory", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "preSurgeryToxicitySet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy", "isInherited": true}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapyPreSurgery", "isInherited": true}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "preSurgeryToxicity", "isInherited": true}], "fieldLabelWidth": 400}, {"fieldId": "preSurgeryToxicityPulmonaryEmbolism", "label": "Zatorowość płucna", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "preSurgeryToxicitySet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy", "isInherited": true}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapyPreSurgery", "isInherited": true}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "preSurgeryToxicity", "isInherited": true}], "fieldLabelWidth": 400}, {"fieldId": "preSurgeryToxicityAcuteKidneyFailure", "label": "Ostre uszkodzenie <PERSON> (AKI)", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "preSurgeryToxicitySet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy", "isInherited": true}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapyPreSurgery", "isInherited": true}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "preSurgeryToxicity", "isInherited": true}], "fieldLabelWidth": 400}, {"fieldId": "preSurgeryToxicityIntestinalObstruction", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jelit", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "preSurgeryToxicitySet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy", "isInherited": true}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapyPreSurgery", "isInherited": true}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "preSurgeryToxicity", "isInherited": true}], "fieldLabelWidth": 400}, {"fieldId": "preSurgeryToxicityAnaphylaxis", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "preSurgeryToxicitySet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy", "isInherited": true}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapyPreSurgery", "isInherited": true}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "preSurgeryToxicity", "isInherited": true}], "fieldLabelWidth": 400}, {"fieldId": "preSurgeryToxicityMultiOrganFailure", "label": "Niewyd<PERSON><PERSON><PERSON>ć wielonarządowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "preSurgeryToxicitySet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy", "isInherited": true}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapyPreSurgery", "isInherited": true}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "preSurgeryToxicity", "isInherited": true}], "fieldLabelWidth": 400}, {"fieldId": "preSurgeryToxicityHearingImpaired", "label": "Uszkodzenie słuchu", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "preSurgeryToxicitySet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy", "isInherited": true}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapyPreSurgery", "isInherited": true}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "preSurgeryToxicity", "isInherited": true}], "fieldLabelWidth": 400}, {"fieldId": "preSurgeryToxicityVOD", "label": "Choroba wenookluzyjna wątroby (VOD)", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "preSurgeryToxicitySet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy", "isInherited": true}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapyPreSurgery", "isInherited": true}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "preSurgeryToxicity", "isInherited": true}], "fieldLabelWidth": 400}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapyPreSurgery"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "preSurgeryToxicity"}]}]}, {"fieldId": "preSurgeryTreatmentEndDate", "label": "Data zakończenia leczenia przedoperacyjnego", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "chemotherapyPreSurgery", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy", "isInherited": true}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapyPreSurgery", "isInherited": true}], "fieldLabelWidth": 340}]}, {"fieldId": "chemotherapyPostSurgery", "label": "Chemioterapia poopera<PERSON>jna", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "chemotherapy", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}], "children": [{"fieldId": "postSurgeryTreatmentStartDate", "label": "Data rozpoczęcia leczenia pooperacyjnego", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "chemotherapyPostSurgery", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapyPostSurgery"}]}, {"fieldId": "postSurgeryTherapeuticArm", "label": "<PERSON><PERSON><PERSON>", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "chemotherapyPostSurgery", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": true, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Vincristine only", "value": "Vincristine only"}, {"label": "AV-1 as per protocol", "value": "AV-1 as per protocol "}, {"label": "AV-2 as per protocol", "value": "AV-2 as per protocol "}, {"label": "AVD 150 as per protocol", "value": "AVD 150 as per protocol"}, {"label": "AVD 250 as per protocol", "value": "AVD 250 as per protocol"}, {"label": "Carbo/VP16/Cyclo/Doxo", "value": "Carbo/VP16/Cyclo/Doxo"}, {"label": "AV for nephroblastomatosis", "value": "AV for nephroblastomatosis"}, {"label": "Stage IV group D (with HDCT_SCT)*", "value": "Stage IV group D (with HDCT_SCT)*"}, {"label": "Stage IV group D (without HDCT_SCT)*", "value": "Stage IV group D (without HDCT_SCT)*"}, {"label": "Sunitinib", "value": "Sunitinib"}, {"label": "Temsirolimus / Everolimus", "value": "Temsirolimus / Everolimus"}, {"label": "Tivatinib", "value": "Tivatinib"}, {"label": "Interferon", "value": "Interferon"}, {"label": "EURHAB", "value": "EURHAB"}, {"label": "EPSSG", "value": "EPSSG"}, {"label": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapyPostSurgery"}], "fieldWidth": 390, "children": [{"fieldId": "postSurgeryTherpaeuticArmChange", "label": "Inne ramię terapeutyczne lub modyfikacja ramienia terapeutycznego", "errors": [], "type": "text-input-field", "disabled": false, "parentId": "postSurgeryTherapeuticArm", "value": "", "maxTextLength": 255, "placeholder": "", "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapyPostSurgery"}, {"conditionalValue": "<PERSON><PERSON>", "conditionType": "equal", "dependentFieldId": "postSurgeryTherapeuticArm"}], "fieldWidth": 390}], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/16WpKKPboH4KfIvL2TdX2_PGLz430fZIR1FrpB7Gbnp4/edit?tab=t.0#heading=h.t6ctmdsi34ki", "tooltipWidth": 170}, {"fieldId": "postSurgeryToxicity", "label": "Toks<PERSON><PERSON><PERSON>ść w trakcie chemioterapii pooperacyjnego", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "chemotherapyPostSurgery", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapyPostSurgery"}], "children": [{"fieldId": "postSurgeryToxicitySet", "label": "", "errors": [], "type": "fields-set", "disabled": false, "parentId": "postSurgeryToxicity", "fieldsSetItems": [{"fieldId": "postSurgeryToxicityVOD", "label": "VOD", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "postSurgeryToxicitySet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy", "isInherited": true}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapyPostSurgery", "isInherited": true}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "postSurgeryToxicity", "isInherited": true}], "fieldLabelWidth": 400}, {"fieldId": "postSurgeryToxicityHema", "label": "Toks<PERSON>cz<PERSON>ść hematologiczna", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "postSurgeryToxicitySet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy", "isInherited": true}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapyPostSurgery", "isInherited": true}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "postSurgeryToxicity", "isInherited": true}], "fieldLabelWidth": 400}, {"fieldId": "postSyrgeryToxicityFebrileNeutropenia", "label": "Gorączka neutropeniczna", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "postSurgeryToxicitySet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy", "isInherited": true}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapyPostSurgery", "isInherited": true}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "postSurgeryToxicity", "isInherited": true}], "fieldLabelWidth": 400}, {"fieldId": "postSurgeryToxicityInfection", "label": "Infek<PERSON>ja", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "postSurgeryToxicitySet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy", "isInherited": true}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapyPostSurgery", "isInherited": true}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "postSurgeryToxicity", "isInherited": true}], "fieldLabelWidth": 400}, {"fieldId": "postSurgeryToxicityEnteritis", "label": "Toksyczność żołądkowo-jelitowa", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "postSurgeryToxicitySet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy", "isInherited": true}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapyPostSurgery", "isInherited": true}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "postSurgeryToxicity", "isInherited": true}], "fieldLabelWidth": 400}, {"fieldId": "postSurgeryToxicityCardio", "label": "Toksyczność kardiologiczna", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "postSurgeryToxicitySet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy", "isInherited": true}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapyPostSurgery", "isInherited": true}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "postSurgeryToxicity", "isInherited": true}], "fieldLabelWidth": 400}, {"fieldId": "postSurgeryToxicityNeuro", "label": "To<PERSON><PERSON><PERSON><PERSON><PERSON>ć neurologiczna", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "postSurgeryToxicitySet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy", "isInherited": true}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapyPostSurgery", "isInherited": true}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "postSurgeryToxicity", "isInherited": true}], "fieldLabelWidth": 400}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapyPostSurgery"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "postSurgeryToxicity"}]}]}, {"fieldId": "postSurgeryEndDate", "label": "Data zakończenia leczenia pooperacyjengo", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "chemotherapyPostSurgery", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapy", "isInherited": true}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "chemotherapyPostSurgery", "isInherited": true}], "fieldLabelWidth": 340}]}]}, {"fieldId": "radiotherapy", "label": "Radioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "radiotherapyStartDate", "label": "Data rozpoczęcia radioterapii", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "radiotherapy", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "radiotherapy"}]}, {"fieldId": "radiotherapyLocation", "label": "Miejsce naświetlania", "errors": [], "type": "fields-set", "disabled": false, "parentId": "radiotherapy", "fieldsSetItems": [{"fieldId": "radiotherapyTumorArea", "label": "Loża po guzie", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "radiotherapyLocation", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "radiotherapy"}]}, {"fieldId": "radiotherapyAbdomen", "label": "Cała jama brzuszna", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "radiotherapyLocation", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "radiotherapy"}]}, {"fieldId": "radiotherapyLung", "label": "<PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "radiotherapyLocation", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "radiotherapyOther", "label": "<PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "radiotherapyLocation", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "radiotherapy"}]}]}, {"fieldId": "surgery", "label": "Chirurgia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "surgeryGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "surgery", "fieldsGroupItems": [{"fieldId": "surgeryDate", "label": "Data zabiegu", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "surgeryGroup1", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "fieldWidth": 300}, {"fieldId": "surgeryLocation", "label": "Lokalizacja", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "surgeryGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "<PERSON><PERSON><PERSON> nerka", "value": "<PERSON><PERSON><PERSON> nerka"}, {"label": "<PERSON><PERSON> nerka", "value": "<PERSON><PERSON> nerka"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "fieldWidth": 300}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "surgery"}], "fieldWidth": 520, "fieldsGroupColumns": 2}, {"fieldId": "surgeryGroup2", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "surgery", "fieldsGroupItems": [{"fieldId": "surgeryType", "label": "<PERSON><PERSON><PERSON>abi<PERSON>", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "surgeryGroup2", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Nefrektomia całkowita", "value": "Nefrektomia całkowita"}, {"label": "Operacja oszczędzająca (NSS)", "value": "Operacja oszczędzająca (NSS)"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "fieldWidth": 300}, {"fieldId": "surgeryResection", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "surgeryGroup2", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Kompletna", "value": "Kompletna"}, {"label": "Niekompletna", "value": "Niekompletna"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "fieldWidth": 300}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "surgery"}], "fieldWidth": 520, "fieldsGroupColumns": 2}, {"fieldId": "surgeryRenalCapsuleGrosslyIntact", "label": "Torebka nerkowa w dużym stopniu nienaruszona", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "surgery", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "surgery"}]}, {"fieldId": "surgeryResectionMarginInvolved", "label": "Obecność żywego utkania nowotworowego w marginesie resekcji", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "surgery", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "surgery"}]}, {"fieldId": "surgeryRenalVeinT<PERSON>ombosis", "label": "Zakrzepica żył nerkowych", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "surgery", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "surgery"}]}, {"fieldId": "surgeryLymphNode<PERSON><PERSON><PERSON>", "label": "Stan węzłów chłonnych", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "surgery", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Zajęte procesem nowotworowym", "value": "Zajęte procesem nowotworowym"}, {"label": "Niezajęte procesem nowotworowym", "value": "Niezajęte procesem nowotworowym"}, {"label": "Niepewny", "value": "Niepewny"}, {"label": "<PERSON><PERSON> bad<PERSON>", "value": "<PERSON><PERSON> bad<PERSON>"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "surgery"}], "fieldWidth": 300}, {"fieldId": "surgery2", "label": "<PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "surgery", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "surgery"}], "children": [{"fieldId": "surgery2Group1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "surgery2", "fieldsGroupItems": [{"fieldId": "surgery2Date", "label": "Data zabiegu", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "surgery2Group1", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "fieldWidth": 300}, {"fieldId": "surgery2Location", "label": "Lokalizacja zabiegu", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "surgery2Group1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "<PERSON><PERSON> nerka", "value": "<PERSON><PERSON> nerka"}, {"label": "<PERSON><PERSON><PERSON> nerka", "value": "<PERSON><PERSON><PERSON> nerka"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "fieldWidth": 300}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "surgery2"}], "fieldWidth": 520, "fieldsGroupColumns": 2}, {"fieldId": "surgery2Group2", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "surgery2", "fieldsGroupItems": [{"fieldId": "surgery2Type", "label": "<PERSON><PERSON><PERSON>abi<PERSON>", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "surgery2Group2", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Nefrektomia całkowita", "value": "Nefrektomia całkowita"}, {"label": "Operacja oszczędzająca (NSS)", "value": "Operacja oszczędzająca (NSS)"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "fieldWidth": 300}, {"fieldId": "surgery2Resection", "label": "Resekcja zabiegu", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "surgery2Group2", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Kompletna", "value": "Kompletna"}, {"label": "Niekompletna", "value": "Niekompletna"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "fieldWidth": 300}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "surgery2"}], "fieldWidth": 520, "fieldsGroupColumns": 2}, {"fieldId": "surgery2RenalCapsuleGrosslyIntact", "label": "Torebka nerkowa w dużym stopniu nienaruszona", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "surgery2", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "surgery"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "surgery2"}]}, {"fieldId": "surgery2ResectionMarginInvolved", "label": "Obecność żywego utkania nowotworowego w marginesie resekcji", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "surgery2", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "surgery"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "surgery2"}]}, {"fieldId": "surgery2RenalVeinThrombosis", "label": "Zakrzepica żył nerkowych", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "surgery2", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "surgery"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "surgery2"}]}, {"fieldId": "surgery2LymphNodeStatus", "label": "Stan węzłów chłonnych", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "surgery2", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Zajęte procesem nowotworowym", "value": "Zajęte procesem nowotworowym"}, {"label": "Niezajęte procesem nowotworowym", "value": "Niezajęte procesem nowotworowym"}, {"label": "Niepewny", "value": "Niepewny"}, {"label": "<PERSON><PERSON> bad<PERSON>", "value": "<PERSON><PERSON> bad<PERSON>"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "surgery"}, {"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "surgery2"}], "fieldWidth": 300}]}]}, {"fieldId": "hsct", "label": "Przeszczep komórek krwiotwórczych", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "hsctGroup1", "label": "", "errors": [], "type": "fields-group", "disabled": false, "parentId": "hsct", "fieldsGroupItems": [{"fieldId": "hsctDate", "label": "Data przeszczepu", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "hsctGroup1", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "fieldWidth": 300}, {"fieldId": "hsctType", "label": "Typ przeszczepu", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "hsctGroup1", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Auto", "value": "Auto"}, {"label": "Allo", "value": "Allo"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "fieldWidth": 300}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "hsct"}], "fieldWidth": 520, "fieldsGroupColumns": 2}]}, {"fieldId": "hormoneTherapy", "label": "Hormonoterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "immunotherapy", "label": "Immunoterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "targetedTherapy", "label": "<PERSON><PERSON><PERSON> celowana", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "otherTherapy", "label": "<PERSON><PERSON> metody <PERSON>", "errors": [], "type": "text-input-field", "disabled": false, "parentId": null, "value": "", "maxTextLength": 255, "placeholder": "", "renderConditions": [], "fieldWidth": 500}, {"fieldId": "treatmentEndDate", "label": "Data zakończenia leczenia", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": null, "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": []}, {"fieldId": "reasonForEndingTreatment", "label": "Przyczyna zakończenia leczenia", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Wyleczenie", "value": "PZL1"}, {"label": "Toksyczności i działania niepożądane", "value": "PZL2"}, {"label": "Wyczerpanie możliwości terapeutycznych", "value": "PZL3"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "PZL4"}, {"label": "Decyzja rodzica", "value": "PZL5"}, {"label": "Zgon", "value": "PZL6"}, {"label": "<PERSON><PERSON>", "value": "PZL7"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "fieldWidth": 400}, {"fieldId": "toxicity", "label": "Toksyczność na koniec leczenia", "errors": [], "type": "fields-set", "disabled": false, "parentId": null, "fieldsSetItems": [{"fieldId": "kidneyFunction", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ne<PERSON>", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "toxicity", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Prawidłowa bez leczenia i bez przeszczepu", "value": "Prawidłowa bez leczenia i bez przeszczepu"}, {"label": "Łagodnie nieprawidłowa, bez leczenia", "value": "Łagodnie nieprawidłowa, bez leczenia"}, {"label": "Łagodnie nieprawidłowa, w trakcie leczenia", "value": "Łagodnie nieprawidłowa, w trakcie leczenia"}, {"label": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"label": "<PERSON><PERSON><PERSON>łowa, po przeszczepie", "value": "<PERSON><PERSON><PERSON>łowa, po przeszczepie"}, {"label": "Ni<PERSON><PERSON><PERSON>łowa, po przeszczepie", "value": "Ni<PERSON><PERSON><PERSON>łowa, po przeszczepie"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "fieldWidth": 400}, {"fieldId": "heartFunction", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> serca", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "toxicity", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>, nie wymaga leczenia", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>, nie wymaga leczenia"}, {"label": "Łagodnie nieprawidłowa, nie wymaga leczenia", "value": "Łagodnie nieprawidłowa, nie wymaga leczenia"}, {"label": "Nieprawidłowa wymagająca leczenia", "value": "Nieprawidłowa wymagająca leczenia"}, {"label": "Prawidłowa z leczeniem", "value": "Prawidłowa z leczeniem"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "fieldWidth": 400}], "renderConditions": []}, {"fieldId": "responseMetastases", "label": "Odpowiedź na leczenie przerzutów", "errors": [], "type": "fields-group-table", "disabled": false, "parentId": null, "value": [{"metastasisLocation": null, "diseaseStatus": null}], "fieldsGroupRowItems": [{"fieldId": "metastasisLocation", "label": "Lokalizacja przerzutu", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "responseMetastases", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"label": "Wątroba", "value": "Wątroba"}, {"label": "Brzuch", "value": "Brzuch"}, {"label": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"label": "Węzły pozabrzuszne", "value": "Węzły pozabrzuszne"}, {"label": "Tkanki <PERSON>", "value": "Tkanki <PERSON>"}, {"label": "Mózg", "value": "Mózg"}, {"label": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}, {"fieldId": "diseaseStatus", "label": "Status", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "responseMetastases", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Całkowita remisja", "value": "Całkowita remisja"}, {"label": "Bardzo dobra częściowa remisja", "value": "Bardzo dobra częściowa remisja"}, {"label": "Częściowa remisja", "value": "Częściowa remisja"}, {"label": "Stabilna choroba", "value": "Stabilna choroba"}, {"label": "Progresja", "value": "Progresja"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "fieldWidth": 300}], "fieldsGroupTableUniqueValuesFromSelect": [], "renderConditions": [], "fieldWidth": 600}, {"fieldId": "patientStatus", "label": "Status pacjenta", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "SD", "value": "SD"}, {"label": "CR", "value": "CR"}, {"label": "Progresja", "value": "Progresja"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "children": [{"fieldId": "sdDate", "label": "Data potwierdzenia stabilnej choroby", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "patientStatus", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "SD", "conditionType": "equal", "dependentFieldId": "patientStatus"}]}, {"fieldId": "crDate", "label": "Data remisji całkowitej", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "patientStatus", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "CR", "conditionType": "equal", "dependentFieldId": "patientStatus"}]}, {"fieldId": "progressionDate", "label": "Data progresji", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "patientStatus", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "Progresja", "conditionType": "equal", "dependentFieldId": "patientStatus"}]}, {"fieldId": "progressionType", "label": "<PERSON><PERSON> prog<PERSON>", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "patientStatus", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Lokalna", "value": "Lokalna"}, {"label": "Odległa", "value": "Odległa"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "Progresja", "conditionType": "equal", "dependentFieldId": "patientStatus"}]}, {"fieldId": "progresionTreatment", "label": "<PERSON><PERSON> prog<PERSON>", "errors": [], "type": "fields-set", "disabled": false, "parentId": "patientStatus", "fieldsSetItems": [{"fieldId": "progresionTreatmentChemotherapy", "label": "Chemioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "progresionTreatment", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "Progresja", "conditionType": "equal", "dependentFieldId": "patientStatus", "isInherited": true}]}, {"fieldId": "progresionTreatmentRadiotherapy", "label": "Radioterapia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "progresionTreatment", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "Progresja", "conditionType": "equal", "dependentFieldId": "patientStatus", "isInherited": true}]}, {"fieldId": "progresionTreatmentHSCT", "label": "Przeszczep", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "progresionTreatment", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "Progresja", "conditionType": "equal", "dependentFieldId": "patientStatus", "isInherited": true}]}, {"fieldId": "progresionTreatmentSurgery", "label": "Chirurgia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "progresionTreatment", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "Progresja", "conditionType": "equal", "dependentFieldId": "patientStatus", "isInherited": true}]}], "renderConditions": [{"conditionalValue": "Progresja", "conditionType": "equal", "dependentFieldId": "patientStatus"}]}, {"fieldId": "progressionTreatmentEndDate", "label": "Data zakończenia leczenia progresji", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "patientStatus", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "Progresja", "conditionType": "equal", "dependentFieldId": "patientStatus"}]}, {"fieldId": "progressionTreatmentResponse", "label": "Odpowiedź na leczenie progresji", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "patientStatus", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "CR", "value": "CR"}, {"label": "Progresja", "value": "Progresja"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "Progresja", "conditionType": "equal", "dependentFieldId": "patientStatus"}]}], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/16WpKKPboH4KfIvL2TdX2_PGLz430fZIR1FrpB7Gbnp4/edit?tab=t.0#heading=h.lusnixbg7erx", "tooltipWidth": 170}, {"fieldId": "comment", "label": "", "errors": [], "type": "textarea-input-field", "disabled": false, "parentId": null, "value": "", "textAreaInputRows": 4, "maxTextLength": 1000, "placeholder": "", "renderConditions": []}]}