{"formTitle": "Diag<PERSON>za", "sections": [{"title": "CHOROBY WSPÓŁISTNIEJĄCE", "rootFieldsIds": ["predispositionToCancer", "predispositionToCancerInFamily", "cancerInTheFamily", "assessmentOfThePatientsCondition"]}, {"title": "OPIS NOWOTWORU", "rootFieldsIds": ["dateOfDiagnosis", "descriptionAndLocationOfTheTumor", "primaryTumour", "diseaseStatus", "metastases", "cancerStage"]}, {"title": "BIOPSJA", "rootFieldsIds": ["biopsy"]}, {"title": "DIAGNOZA HISTOPATOLOGICZNA", "rootFieldsIds": ["dateOfHistPatDiagnosis", "pathologicalDiagnosis", "clinicalStageOfCancer", "histPatDiagnosis"]}, {"title": "KLASYFIKACJA", "rootFieldsIds": ["icd10", "iccc3"]}, {"title": "KOMENTARZ", "rootFieldsIds": ["comment"]}], "fields": [{"fieldId": "predispositionToCancer", "label": "Predyspozycje do nowotworzenia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "predispositionToCancerSet", "label": "", "errors": [], "type": "fields-set", "disabled": false, "parentId": "predispositionToCancer", "fieldsSetItems": [{"fieldId": "predispositionToCancerAniridia", "label": "Aniridia/WAGR", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "predispositionToCancerSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "predispositionToCancer"}], "fieldLabelWidth": 250}, {"fieldId": "predispositionToCancerHemi", "label": "Hemihipertrofia", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "predispositionToCancerSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "predispositionToCancer"}], "fieldLabelWidth": 250}, {"fieldId": "predispositionToCancerDenys", "label": "Zespół Denysa-<PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "predispositionToCancerSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "predispositionToCancer"}], "fieldLabelWidth": 250}, {"fieldId": "predisposition<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "predispositionToCancerSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "predispositionToCancer"}], "fieldLabelWidth": 250}, {"fieldId": "predispositionToCancerUrogenitial", "label": "Wady rozwojowe układu moczowo-płciowego", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "predispositionToCancerSet", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "predispositionToCancer"}], "fieldLabelWidth": 250}, {"fieldId": "predispositionToCancer<PERSON><PERSON>", "label": "<PERSON><PERSON>", "errors": [], "type": "textarea-input-field", "disabled": false, "parentId": "predispositionToCancerSet", "value": "", "textAreaInputRows": 4, "maxTextLength": 1000, "placeholder": "", "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "predispositionToCancer"}]}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "predispositionToCancer"}]}]}, {"fieldId": "predispositionToCancerInFamily", "label": "Predyspozycje do nowotworzenia wśród członków rodziny w 1. i 2. linii", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": []}, {"fieldId": "cancerInTheFamily", "label": "<PERSON><PERSON><PERSON> nowotworowe w rodzinie", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "cancerInTheFamilyDetails", "label": "Opis szczegółowy", "errors": [], "type": "textarea-input-field", "disabled": false, "parentId": "cancerInTheFamily", "value": "", "textAreaInputRows": 4, "maxTextLength": 1000, "placeholder": "", "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "cancerInTheFamily"}]}]}, {"fieldId": "assessmentOfThePatientsCondition", "label": "<PERSON><PERSON>na stanu pacjenta wg s<PERSON><PERSON>/<PERSON>nsky", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": true, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "0", "value": "0"}, {"label": "10", "value": "10"}, {"label": "20", "value": "20"}, {"label": "30", "value": "30"}, {"label": "40", "value": "40"}, {"label": "50", "value": "50"}, {"label": "60", "value": "60"}, {"label": "70", "value": "70"}, {"label": "80", "value": "80"}, {"label": "90", "value": "90"}, {"label": "100", "value": "100"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": []}, {"fieldId": "dateOfDiagnosis", "label": "Data wstępnego rozpoznania", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": null, "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": []}, {"fieldId": "descriptionAndLocationOfTheTumor", "label": "Opis i lokalizacja nowotworu", "errors": [], "type": "textarea-input-field", "disabled": false, "parentId": null, "value": "", "textAreaInputRows": 4, "maxTextLength": 1000, "placeholder": "", "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/16WpKKPboH4KfIvL2TdX2_PGLz430fZIR1FrpB7Gbnp4/edit?tab=t.0#heading=h.vslv4z6b7mar", "tooltipWidth": 170}, {"fieldId": "primaryTumour", "label": "Guz pierwotny", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": true, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "<PERSON><PERSON><PERSON> nerka", "value": "<PERSON><PERSON><PERSON> nerka"}, {"label": "<PERSON><PERSON> nerka", "value": "<PERSON><PERSON> nerka"}, {"label": "<PERSON><PERSON> ne<PERSON>i", "value": "<PERSON><PERSON> ne<PERSON>i"}, {"label": "Po<PERSON><PERSON><PERSON>wy", "value": "Po<PERSON><PERSON><PERSON>wy"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": []}, {"fieldId": "diseaseStatus", "label": "Status choroby (wg. <PERSON>brella SIOP RTSG 2016)", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": true, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Guz zlokalizowany", "value": "Guz zlokalizowany"}, {"label": "Guz zlokalizowany (przerzuty <=3mm, tylko \"CT\")", "value": "Guz zlokalizowany (przerzuty <=3mm, tylko \"CT\")"}, {"label": "Guz z przerzutami (zmiany 3-5mm)", "value": "Guz z przerzutami (zmiany 3-5mm)"}, {"label": "Guz z przerzutami (zmiany >5mm)", "value": "Guz z przerzutami (zmiany >5mm)"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "fieldWidth": 400}, {"fieldId": "metastases", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "metastaticSites", "label": "Lokalizacja przerzutów", "errors": [], "type": "fields-set", "disabled": false, "parentId": "metastases", "fieldsSetItems": [{"fieldId": "metastaticSitesLung", "label": "<PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "metastases", "isInherited": true}], "fieldLabelWidth": 250}, {"fieldId": "metastaticSitesLiver", "label": "Wątroba", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "metastases", "isInherited": true}], "fieldLabelWidth": 250}, {"fieldId": "metastaticSitesAbdomen", "label": "<PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "metastases", "isInherited": true}], "fieldLabelWidth": 250}, {"fieldId": "metastaticSitesBone", "label": "<PERSON><PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "metastases", "isInherited": true}], "fieldLabelWidth": 250}, {"fieldId": "metastaticSitesExtraAbdominal", "label": "Węzły chłonne pozabrzuszone", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "metastases", "isInherited": true}], "fieldLabelWidth": 250}, {"fieldId": "metastaticSitesSoftTissues", "label": "Tkanki <PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "metastases", "isInherited": true}], "fieldLabelWidth": 250}, {"fieldId": "metastaticSitesBrain", "label": "Mózg", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "metastases", "isInherited": true}], "fieldLabelWidth": 250}, {"fieldId": "metastaticSitesOther", "label": "<PERSON><PERSON>", "errors": [], "type": "three-state-level", "disabled": false, "parentId": "metastaticSites", "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "metastases", "isInherited": true}], "fieldLabelWidth": 250}], "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "metastases"}]}]}, {"fieldId": "cancerStage", "label": "Stopień zaawansowania nowotworu zgodnie z klasyfikacjami TNM", "errors": [], "type": "fields-group", "disabled": false, "parentId": null, "fieldsGroupItems": [{"fieldId": "cancerStageClassificationsT", "label": "T", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "cancerStage", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": true, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "T0", "value": "T0"}, {"label": "T1", "value": "T1"}, {"label": "T1a", "value": "T1a"}, {"label": "T1b", "value": "T1b"}, {"label": "T2", "value": "T2"}, {"label": "T2a", "value": "T2a"}, {"label": "T2b", "value": "T2b"}, {"label": "TX", "value": "TX"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}, {"fieldId": "cancerStageClassificationsN", "label": "N", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "cancerStage", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": true, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "N0", "value": "N0"}, {"label": "N1", "value": "N1"}, {"label": "NX", "value": "NX"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}, {"fieldId": "cancerStageClassificationsM", "label": "M", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "cancerStage", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": true, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "M0", "value": "M0"}, {"label": "M1", "value": "M1"}, {"label": "MX", "value": "MX"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false}], "renderConditions": [], "fieldWidth": 545}, {"fieldId": "biopsy", "label": "Biopsja", "errors": [], "type": "three-state-level", "disabled": false, "parentId": null, "value": "not-selected", "threeStateLevelOptions": [{"label": "Tak", "value": "yes"}, {"label": "", "value": "not-selected"}, {"label": "<PERSON><PERSON>", "value": "no"}], "renderConditions": [], "children": [{"fieldId": "dateOfBiopsy", "label": "Data biopsji", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": "biopsy", "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "biopsy"}]}, {"fieldId": "typeOfBiopsy", "label": "Rodzaj biopsji", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "biopsy", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": true, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "Biopsja cienkoigłowa", "value": "Biopsja cienkoigłowa"}, {"label": "Otwarte – biopsja chirurgiczna", "value": "Otwarte – biopsja chirurgiczna"}, {"label": "Przezskórna biopsja igłowa tnąca (Tru-cut)", "value": "Przezskórna biopsja igłowa tnąca (Tru-cut)"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "yes", "conditionType": "equal", "dependentFieldId": "biopsy"}], "fieldWidth": 400}]}, {"fieldId": "dateOfHistPatDiagnosis", "label": "Data diagnozy histopatologicznej", "errors": [], "type": "date-picker-field", "disabled": false, "parentId": null, "value": null, "datePickerMaxDate": "", "datePickerMinDate": "", "datePickerFormat": "yyyy-MM-dd", "datePickerMaxNowDate": true, "renderConditions": []}, {"fieldId": "pathologicalDiagnosis", "label": "Rozpoznanie patomorfologiczne", "errors": [], "type": "ckeditor-input-field", "disabled": false, "parentId": null, "value": "", "maxTextLength": 1000, "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/16WpKKPboH4KfIvL2TdX2_PGLz430fZIR1FrpB7Gbnp4/edit?tab=t.0#heading=h.f9ix0meqdkrf", "tooltipWidth": 170}, {"fieldId": "clinicalStageOfCancer", "label": "Stadium kliniczne zaawansowania nowotworu (wg. Umbrella SIOP RTSG 2016)", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": false, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "nie dot<PERSON>y", "value": "nie dot<PERSON>y"}, {"label": "I", "value": "I"}, {"label": "II", "value": "II"}, {"label": "III", "value": "III"}, {"label": "IV", "value": "IV"}, {"label": "V", "value": "V"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "children": [{"fieldId": "clinicalStageOfCancerIVSet", "label": "", "errors": [], "type": "fields-set", "disabled": false, "parentId": "clinicalStageOfCancer", "fieldsSetItems": [{"fieldId": "localStadium", "label": "Stadium lokalne", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "clinicalStageOfCancerIV", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "0", "value": "0"}, {"label": "I", "value": "I"}, {"label": "II", "value": "II"}, {"label": "III", "value": "III"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "IV", "conditionType": "equal", "dependentFieldId": "clinicalStageOfCancer"}]}], "renderConditions": [{"conditionalValue": "IV", "conditionType": "equal", "dependentFieldId": "clinicalStageOfCancer"}]}, {"fieldId": "stageOfKidneySet", "label": "", "errors": [], "type": "fields-set", "disabled": false, "parentId": "clinicalStageOfCancer", "fieldsSetItems": [{"fieldId": "stageOfRightKidney", "label": "<PERSON><PERSON><PERSON> nerka", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "stageOfKidneySet", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "0", "value": "0"}, {"label": "I", "value": "I"}, {"label": "II", "value": "II"}, {"label": "III", "value": "III"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "V", "conditionType": "equal", "dependentFieldId": "clinicalStageOfCancer"}]}, {"fieldId": "stageOfLeftKidney", "label": "<PERSON><PERSON> nerka", "errors": [], "type": "select-input-field", "disabled": false, "parentId": "stageOfKidneySet", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "0", "value": "0"}, {"label": "I", "value": "I"}, {"label": "II", "value": "II"}, {"label": "III", "value": "III"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [{"conditionalValue": "V", "conditionType": "equal", "dependentFieldId": "clinicalStageOfCancer"}]}], "renderConditions": [{"conditionalValue": "V", "conditionType": "equal", "dependentFieldId": "clinicalStageOfCancer"}]}], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/16WpKKPboH4KfIvL2TdX2_PGLz430fZIR1FrpB7Gbnp4/edit?tab=t.0#heading=h.czkho6lu9w2p", "tooltipWidth": 170}, {"fieldId": "histPatDiagnosis", "label": "Diagnoza histopatologiczna", "errors": [], "type": "select-input-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "searchable": false, "showSelectAll": false, "selectMode": "single", "canDeselect": false, "selectOptions": [{"label": "<PERSON><PERSON><PERSON> nerka", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"label": "<PERSON><PERSON> nerka", "value": "leftKidney"}, {"label": "<PERSON><PERSON> ne<PERSON>i", "value": "bothKidneys"}], "placeholder": "", "selectUsePatientProtocolsAsOptions": false, "renderConditions": [], "children": [{"fieldId": "rightKidneySet", "label": "", "errors": [], "type": "fields-set", "disabled": false, "parentId": "histPatDiagnosis", "fieldsSetItems": [{"fieldId": "histPatDiagnosisRightKidney", "label": "Prawa nerka rozpoznanie", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "rightKidneySet", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "WT-DIAGNOSIS-01", "liveSelectItemsPerPage": 100, "placeholder": "", "renderConditions": [{"conditionType": "equal", "dependentFieldId": "histPatDiagnosis", "conditionalValue": "<PERSON><PERSON><PERSON><PERSON>"}, {"conditionType": "equal", "dependentFieldId": "histPatDiagnosis", "conditionalValue": "<PERSON><PERSON><PERSON><PERSON>", "isInherited": true}]}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "histPatDiagnosis", "conditionalValue": "<PERSON><PERSON><PERSON><PERSON>"}]}, {"fieldId": "leftKidneySet", "label": "", "errors": [], "type": "fields-set", "disabled": false, "parentId": "histPatDiagnosis", "fieldsSetItems": [{"fieldId": "histPatDiagnosisLeftKidney", "label": "Lewa nerka rozpoznanie", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "leftKidneySet", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "WT-DIAGNOSIS-01", "liveSelectItemsPerPage": 100, "placeholder": "", "renderConditions": [{"conditionType": "equal", "dependentFieldId": "histPatDiagnosis", "conditionalValue": "leftKidney"}, {"conditionType": "equal", "dependentFieldId": "histPatDiagnosis", "conditionalValue": "leftKidney", "isInherited": true}]}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "histPatDiagnosis", "conditionalValue": "leftKidney"}]}, {"fieldId": "bothKidneysSet", "label": "", "errors": [], "type": "fields-set", "disabled": false, "parentId": "histPatDiagnosis", "fieldsSetItems": [{"fieldId": "histPatDiagnosisBothKidneysRight", "label": "Prawa nerka rozpoznanie", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "bothKidneysSet", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "WT-DIAGNOSIS-01", "liveSelectItemsPerPage": 100, "placeholder": "", "renderConditions": [{"conditionType": "equal", "dependentFieldId": "histPatDiagnosis", "conditionalValue": "bothKidneys"}, {"conditionType": "equal", "dependentFieldId": "histPatDiagnosis", "conditionalValue": "bothKidneys", "isInherited": true}]}, {"fieldId": "histPatDiagnosisBothKidneysLeft", "label": "Lewa nerka rozpoznanie", "errors": [], "type": "live-select-field", "disabled": false, "parentId": "bothKidneysSet", "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "WT-DIAGNOSIS-01", "liveSelectItemsPerPage": 100, "placeholder": "", "renderConditions": [{"conditionType": "equal", "dependentFieldId": "histPatDiagnosis", "conditionalValue": "bothKidneys"}, {"conditionType": "equal", "dependentFieldId": "histPatDiagnosis", "conditionalValue": "bothKidneys", "isInherited": true}]}], "renderConditions": [{"conditionType": "equal", "dependentFieldId": "histPatDiagnosis", "conditionalValue": "bothKidneys"}]}], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/16WpKKPboH4KfIvL2TdX2_PGLz430fZIR1FrpB7Gbnp4/edit?tab=t.0#heading=h.os6ddqu3gx6g", "tooltipWidth": 170}, {"fieldId": "icd10", "label": "ICD-10", "errors": [], "type": "live-select-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "ICD10", "liveSelectItemsPerPage": 100, "placeholder": "", "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/16WpKKPboH4KfIvL2TdX2_PGLz430fZIR1FrpB7Gbnp4/edit?tab=t.0#heading=h.y833k84nyjxk", "tooltipWidth": 170}, {"fieldId": "iccc3", "label": "ICCC-3", "errors": [], "type": "live-select-field", "disabled": false, "parentId": null, "value": null, "canCleanSelect": true, "openSelectDirection": "bottom", "closeOnSelect": true, "liveSelectDictionary": "ICCC", "liveSelectItemsPerPage": 100, "placeholder": "", "renderConditions": [], "tooltipText": "Więcej informacji", "tooltipLink": "https://docs.google.com/document/d/16WpKKPboH4KfIvL2TdX2_PGLz430fZIR1FrpB7Gbnp4/edit?tab=t.0#heading=h.12ycsmwm35w7", "tooltipWidth": 170}, {"fieldId": "comment", "label": "Komentarz", "errors": [], "type": "textarea-input-field", "disabled": false, "parentId": null, "value": "", "textAreaInputRows": 4, "maxTextLength": 1000, "placeholder": "", "renderConditions": []}]}