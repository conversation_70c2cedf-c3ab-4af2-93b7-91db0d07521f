<?php

declare(strict_types=1);

namespace App\Account\Application\Query;

use App\Common\QueryView;

class AccountView implements QueryView
{
    public function __construct(
        private readonly string $accountId,
        private readonly string $firstName,
        private readonly string $lastName,
        private readonly string $email,
        private readonly string $mobilePhoneNumber,
        private readonly string $role,
        private readonly bool $isActive,
        private readonly ?string $deactivationReason,
        /** @var array<string> */
        private array $protocols,
        /** @var array<string> */
        private array $hospitals,
        private readonly \DateTimeImmutable $createdAt,
        private readonly ?\DateTimeImmutable $updatedAt = null,
        private ?\DateTimeImmutable $loginAt = null,
    ) {
    }

    /** @param array<string, mixed> $data */
    public static function deserialize(array $data): self
    {
        return new self(
            $data['accountId'],
            $data['firstName'],
            $data['lastName'],
            $data['email'],
            $data['mobilePhoneNumber'],
            $data['role'],
            (bool) $data['isActive'],
            $data['deactivationReason'] ?? null,
            json_decode($data['protocols'], true, 2, JSON_THROW_ON_ERROR),
            json_decode($data['hospitals'], true, 2, JSON_THROW_ON_ERROR),
            new \DateTimeImmutable($data['createdAt']),
            isset($data['updatedAt']) ? new \DateTimeImmutable($data['updatedAt']) : null,
            isset($data['loginAt']) ? new \DateTimeImmutable($data['loginAt']) : null,
        );
    }

    public function jsonSerialize(): mixed
    {
        return [
            'accountId' => $this->accountId,
            'firstName' => $this->firstName,
            'lastName' => $this->lastName,
            'email' => $this->email,
            'mobilePhoneNumber' => $this->mobilePhoneNumber,
            'role' => $this->role,
            'isActive' => $this->isActive,
            'deactivationReason' => $this->deactivationReason,
            'protocols' => $this->protocols,
            'hospitals' => $this->hospitals,
            'createdAt' => $this->createdAt->format('Y-m-d H:i:s'),
            'updatedAt' => $this->updatedAt?->format('Y-m-d H:i:s'),
            'loginAt' => $this->loginAt?->format('Y-m-d H:i:s'),
        ];
    }

    /** @param array<string> $protocols */
    public function setProtocols(array $protocols): void
    {
        $this->protocols = $protocols;
    }

    /** @param array<string> $hospitals */
    public function setHospitals(array $hospitals): void
    {
        $this->hospitals = $hospitals;
    }

    public function setLoginAt(?\DateTimeImmutable $loginAt): void
    {
        $this->loginAt = $loginAt;
    }

    public function accountId(): string
    {
        return $this->accountId;
    }

    public function firstName(): string
    {
        return $this->firstName;
    }

    public function lastName(): string
    {
        return $this->lastName;
    }

    public function email(): string
    {
        return $this->email;
    }

    public function mobilePhoneNumber(): string
    {
        return $this->mobilePhoneNumber;
    }

    public function role(): string
    {
        return $this->role;
    }

    public function isActive(): bool
    {
        return $this->isActive;
    }

    public function deactivationReason(): ?string
    {
        return $this->deactivationReason;
    }

    /** @return array<string> */
    public function protocols(): array
    {
        return $this->protocols;
    }

    /** @return array<string> */
    public function hospitals(): array
    {
        return $this->hospitals;
    }

    public function createdAt(): \DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function updatedAt(): ?\DateTimeImmutable
    {
        return $this->updatedAt;
    }
}
