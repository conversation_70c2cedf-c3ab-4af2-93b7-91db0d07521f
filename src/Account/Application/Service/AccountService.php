<?php

declare(strict_types=1);

namespace App\Account\Application\Service;

use App\Account\Application\Exception\AccountCannotBeCreated;
use App\Account\Application\Exception\AccountCannotBeUpdated;
use App\Account\Application\Exception\AccountEmailCannotBeChanged;
use App\Account\Application\Query\AccountQueryInterface;
use App\Account\Application\Query\HospitalQueryInterface;
use App\Account\Domain\Account;
use App\Account\Domain\AccountRepositoryInterface;
use App\Auth\Application\AuthAccountFacade;
use App\Common\Uuid;
use App\SharedKernel\Email;
use App\SharedKernel\Roles;

readonly class AccountService
{
    public function __construct(
        private AccountQueryInterface $accountQuery,
        private AccountRepositoryInterface $accountRepository,
        private AuthAccountFacade $authAccountFacade,
        private HospitalQueryInterface $hospitalQuery,
    ) {
    }

    /**
     * @param array<string> $protocols
     * @param array<string> $hospitals
     */
    public function createAccount(Uuid $accountId, string $firstName, string $lastName,
        Email $email,
        string $mobilePhoneNumber,
        Roles $role,
        array $protocols,
        array $hospitals,
    ): void {
        if ($this->accountQuery->checkIfEmailExists($email->valueString()) === true) {
            throw AccountCannotBeCreated::becauseThisEmailAsAlreadyInUse($email->valueString());
        }

        if ($this->accountQuery->checkIfMobilePhoneNumberExists($mobilePhoneNumber) === true) {
            throw AccountCannotBeCreated::becauseThisMobilePhoneNumberAsAlreadyInUse($mobilePhoneNumber);
        }

        if (!$this->validateHospitals($hospitals)) {
            throw AccountCannotBeCreated::becauseOneOrMoreHospitalsDoNotExist();
        }

        $account = match ($role) {
            Roles::ROLE_CENTRAL_ADMINISTRATOR => Account::createCentralAdminAccount(
                $accountId,
                $firstName,
                $lastName,
                $email,
                $mobilePhoneNumber
            ),
            Roles::ROLE_REPORTER_PHYSICIAN => Account::createReportingPhysician(
                $accountId,
                $firstName,
                $lastName,
                $email,
                $mobilePhoneNumber,
                $protocols,
                $hospitals
            ),
            Roles::ROLE_COORDINATOR => Account::createCoordinator(
                $accountId,
                $firstName,
                $lastName,
                $email,
                $mobilePhoneNumber,
                $protocols
            ),
            Roles::ROLE_DATA_ADMINISTRATOR => Account::createDataAdministrator(
                $accountId,
                $firstName,
                $lastName,
                $email,
                $mobilePhoneNumber,
                $hospitals
            ),
        };

        $this->accountRepository->add($account);
        $this->authAccountFacade->createAuthUser($accountId->valueString(), $email->valueString(), $role->value, $account->isActive());
    }

    /**
     * @param array<string> $protocols
     * @param array<string> $hospitals
     */
    public function update(
        Uuid $accountId,
        string $firstName,
        string $lastName,
        string $mobilePhoneNumber,
        array $protocols,
        array $hospitals,
    ): void {
        $account = $this->accountRepository->getByAccountId($accountId);

        if ($this->accountQuery->checkIfEmailExists($account->email()->valueString(), $accountId->valueString()) === true) {
            throw AccountCannotBeUpdated::becauseThisEmailAsAlreadyInUse($account->email()->valueString());
        }

        if ($this->accountQuery->checkIfMobilePhoneNumberExists($mobilePhoneNumber, $accountId->valueString()) === true) {
            throw AccountCannotBeUpdated::becauseThisMobilePhoneNumberAsAlreadyInUse($mobilePhoneNumber);
        }

        if (!$this->validateHospitals($hospitals)) {
            throw AccountCannotBeUpdated::becauseOneOrMoreHospitalsDoNotExist();
        }

        match ($account->role()) {
            Roles::ROLE_CENTRAL_ADMINISTRATOR => $account->updateCentralAdminAccount(
                $firstName,
                $lastName,
                $mobilePhoneNumber
            ),
            Roles::ROLE_REPORTER_PHYSICIAN => $account->updateReportingPhysician(
                $firstName,
                $lastName,
                $mobilePhoneNumber,
                $protocols,
                $hospitals
            ),
            Roles::ROLE_COORDINATOR => $account->updateCoordinator(
                $firstName,
                $lastName,
                $mobilePhoneNumber,
                $protocols
            ),
            Roles::ROLE_DATA_ADMINISTRATOR => $account->updateDataAdministrator(
                $firstName,
                $lastName,
                $mobilePhoneNumber,
                $hospitals
            ),
        };

        $this->accountRepository->update($account);
    }

    public function changeEmail(Uuid $accountId, string $email): void
    {
        $account = $this->accountRepository->getByAccountId($accountId);

        if ($this->accountQuery->checkIfEmailExists($email, $accountId->valueString()) === true) {
            throw AccountEmailCannotBeChanged::becauseThisEmailAsAlreadyInUse($email);
        }

        $account->changeEmail(Email::create($email));

        $this->accountRepository->update($account);

        $this->authAccountFacade->changeAuthUserEmail($accountId->valueString(), $email);
    }

    public function activate(Uuid $accountId): void
    {
        $account = $this->accountRepository->getByAccountId($accountId);

        $account->activate();

        $this->accountRepository->update($account);
        $this->authAccountFacade->activateAuthUser($accountId->valueString());
    }

    public function deactivate(Uuid $accountId, string $reason): void
    {
        $account = $this->accountRepository->getByAccountId($accountId);

        $account->deactivate($reason);

        $this->accountRepository->update($account);
        $this->authAccountFacade->deactivateAuthUser($accountId->valueString());
    }

    /** @param array<string> $hospitals */
    private function validateHospitals(array $hospitals): bool
    {
        $hospitalIds = $this->hospitalQuery->findAllActiveHospitalSelect();

        foreach ($hospitals as $hospitalId) {
            if (!$hospitalIds->containsKey($hospitalId)) {
                return false;
            }
        }

        return true;
    }
}
