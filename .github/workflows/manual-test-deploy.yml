name: TEST - Manual Deploy instance 🚀

on:
  workflow_dispatch:
    branches:
      - test
    inputs:
      version:
        description: 'Wersja'
        required: true
        default: 'patch'
        type: choice
        options:
          - major
          - minor
          - patch

jobs:
  build-test-and-deploy:
    name: Symfony  (PHP ${{ matrix.php-versions }} on ${{ matrix.operating-system }})
    runs-on: ${{ matrix.operating-system }}
    strategy:
      fail-fast: false
      matrix:
        operating-system: [ ubuntu-22.04 ]
        php-versions: [ '8.3' ]
    steps:
      # Mandatory : fetch the current repository
      - name: Checkout
        uses: actions/checkout@v4

      # Docs: https://github.com/shivammathur/setup-php
      - name: Setup PHP, with composer and extensions
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-versions }}
          extensions: mbstring, xml, ctype, iconv, intl, pdo_mysql, json, fileinfo
        env:
          update: true

      # To be faster, use a cache system for the Composer
      - name: Get composer cache directory
        id: composer-cache
        run: echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT

      - name: Cache composer dependencies
        uses: actions/cache@v4
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: ${{ runner.os }}-composer-

      # Ensure that composer.json is valid
      - name: Validate composer.json and composer.lock
        run: composer validate

      - name: Install Composer dependencies
        run: composer install --no-progress --prefer-dist --optimize-autoloader

      - name: Run Check CS
        run: |
          mv .php-cs-fixer.dist.php .php-cs-fixer.php
          vendor/bin/php-cs-fixer fix --diff --allow-risky=yes --dry-run --config .php-cs-fixer.php

      - name: Generate Symfony container
        run: php bin/console lint:container --env dev

      - name: Run tests
        run: composer tests:unit

      - name: Run PHPStan (static analyze)
        run: |
          mv phpstan.neon.dist phpstan.neon
          composer static:analyze

      - name: Git configuration 🛠️
        run: |
          git config --global user.email github-actions[bot]@users.noreply.github.com
          git config --global user.name github-actions[bot]

      # Bump package version
      - name: Bump version 📈
        run: |
          echo "NEW_VERSION=$(npm --no-git-tag-version version ${{ github.event.inputs.version }})" >> $GITHUB_ENV

      # Commit changes
      - name: Commit package.json and changelog.md changes and create tag 🏷️
        run: |
          git add "package.json"
          git commit -m "version ${{ env.NEW_VERSION }}"
          git tag ${{ env.NEW_VERSION }}

      # Push repository changes
      - name: Push changes to repository 🚀
        run: |
          git push origin && git push --tag

      - name: Zip artifact 📦
        run: |
          rm -rf ./bin/docker-start.sh ./bin/rebuild-db.sh ./bin/run-bash.sh ./bin/run-zsh.sh ./var/cache/dev ./var/cache/test
          zip -r hope_r_backend_${{ env.NEW_VERSION }}.zip ./bin ./config ./fixtures ./migrations ./public ./src ./templates ./tests ./vendor ./composer.json ./composer.lock ./README.md ./symfony.lock

      - name: SSH Server Deploy to server test
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.SSH_TEST_HOST }}
          username: ${{ secrets.SSH_LOGIN }}
          key: ${{ secrets.SSH_KEY }}
          port: ${{ secrets.SSH_PORT }}
          source: hope_r_backend_${{ env.NEW_VERSION }}.zip
          target: ${{ secrets.SSH_DEPLOY_PATH }}

      - name: Preparation new application version
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SSH_TEST_HOST }}
          username: ${{ secrets.SSH_LOGIN }}
          key: ${{ secrets.SSH_KEY }}
          port: ${{ secrets.SSH_PORT }}
          script: /bin/bash /home/<USER>/preper_new_backend.sh -n hope_r_backend_${{ env.NEW_VERSION }}.zip -e test
